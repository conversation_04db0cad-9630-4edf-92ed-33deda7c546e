import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isAdmin = mode === 'admin';

  return {
    plugins: [react()],
    optimizeDeps: {
      exclude: ['lucide-react'],
    },
    build: {
      rollupOptions: isAdmin ? {
        input: {
          admin: 'admin.html'
        },
        output: {
          assetFileNames: 'admin/assets/[name]-[hash][extname]',
          chunkFileNames: 'admin/assets/[name]-[hash].js',
          entryFileNames: 'admin/assets/[name]-[hash].js',
        },
      } : {
        output: {
          // Ensure CSS and JS files have unique hashes for cache busting
          assetFileNames: 'assets/[name]-[hash][extname]',
          chunkFileNames: 'assets/[name]-[hash].js',
          entryFileNames: 'assets/[name]-[hash].js',
        },
      },
      // Ensure proper CSS handling
      cssCodeSplit: false,
    },
  };
});
