import React from 'react';
import { Helmet } from 'react-helmet-async';
import { getCurrentClient } from '../../utils/clientConfig';

interface SEOHeadProps {
  title?: string;
  description?: string;
  keywords?: string[];
  canonicalUrl?: string;
  ogImage?: string;
  ogType?: string;
  structuredData?: object;
  noIndex?: boolean;
}

const SEOHead: React.FC<SEOHeadProps> = ({
  title,
  description,
  keywords = [],
  canonicalUrl,
  ogImage,
  ogType = 'website',
  structuredData,
  noIndex = false
}) => {
  const client = getCurrentClient();
  
  if (!client) return null;

  const fullTitle = title || client.seo.title;
  const fullDescription = description || client.seo.description;
  const allKeywords = [...keywords, ...client.seo.keywords];
  const currentUrl = canonicalUrl || (typeof window !== 'undefined' ? window.location.href : '');
  const defaultOgImage = ogImage || `${window.location.origin}/og-image.jpg`;

  return (
    <Helmet>
      {/* Basic Meta Tags */}
      <title>{fullTitle}</title>
      <meta name="description" content={fullDescription} />
      <meta name="keywords" content={allKeywords.join(', ')} />
      
      {/* Canonical URL */}
      {canonicalUrl && <link rel="canonical" href={canonicalUrl} />}
      
      {/* Robots */}
      {noIndex && <meta name="robots" content="noindex, nofollow" />}
      
      {/* Open Graph */}
      <meta property="og:title" content={fullTitle} />
      <meta property="og:description" content={fullDescription} />
      <meta property="og:type" content={ogType} />
      <meta property="og:url" content={currentUrl} />
      <meta property="og:image" content={defaultOgImage} />
      <meta property="og:site_name" content={client.name} />
      
      {/* Twitter Card */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:title" content={fullTitle} />
      <meta name="twitter:description" content={fullDescription} />
      <meta name="twitter:image" content={defaultOgImage} />
      
      {/* Business Information */}
      <meta name="geo.region" content={client.serviceAreas[0]?.state || 'US'} />
      <meta name="geo.placename" content={client.serviceAreas[0]?.city || ''} />
      <meta name="geo.position" content="33.7490;-84.3880" />
      <meta name="ICBM" content="33.7490, -84.3880" />
      
      {/* Contact Information */}
      <meta name="contact" content={client.contact.phone} />
      <meta name="author" content={client.name} />
      
      {/* Favicon */}
      <link rel="icon" type="image/x-icon" href={client.branding.favicon} />
      
      {/* Structured Data */}
      {structuredData && (
        <script type="application/ld+json">
          {JSON.stringify(structuredData)}
        </script>
      )}
    </Helmet>
  );
};

export default SEOHead;
