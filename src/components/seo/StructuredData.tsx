import React from 'react';
import { Helmet } from 'react-helmet-async';
import { getCurrentClient, Service, Location } from '../../utils/clientConfig';

interface StructuredDataProps {
  type: 'business' | 'service' | 'serviceLocation';
  service?: Service;
  location?: Location;
}

const StructuredData: React.FC<StructuredDataProps> = ({ type, service, location }) => {
  const client = getCurrentClient();
  
  if (!client) return null;

  const generateBusinessSchema = () => ({
    "@context": "https://schema.org",
    "@type": "LocalBusiness",
    "name": client.name,
    "description": client.seo.description,
    "url": typeof window !== 'undefined' ? window.location.origin : '',
    "telephone": client.contact.phone,
    "email": client.contact.email,
    "address": {
      "@type": "PostalAddress",
      "streetAddress": client.contact.address.split(',')[0],
      "addressLocality": client.serviceAreas[0]?.city || '',
      "addressRegion": client.serviceAreas[0]?.state || '',
      "postalCode": client.serviceAreas[0]?.zipCodes[0] || ''
    },
    "geo": location ? {
      "@type": "GeoCoordinates",
      "latitude": location.coordinates.lat,
      "longitude": location.coordinates.lng
    } : undefined,
    "areaServed": client.serviceAreas.map(area => ({
      "@type": "City",
      "name": `${area.city}, ${area.state}`
    })),
    "hasOfferCatalog": {
      "@type": "OfferCatalog",
      "name": "Home Services",
      "itemListElement": client.services.map((svc, index) => ({
        "@type": "Offer",
        "itemOffered": {
          "@type": "Service",
          "name": svc.name,
          "description": svc.description
        },
        "position": index + 1
      }))
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.9",
      "reviewCount": "150",
      "bestRating": "5",
      "worstRating": "1"
    },
    "priceRange": "$$",
    "openingHours": [
      "Mo-Fr 08:00-18:00",
      "Sa 09:00-16:00"
    ]
  });

  const generateServiceSchema = () => {
    if (!service) return null;
    
    return {
      "@context": "https://schema.org",
      "@type": "Service",
      "name": service.name,
      "description": service.longDescription,
      "provider": {
        "@type": "LocalBusiness",
        "name": client.name,
        "telephone": client.contact.phone,
        "url": typeof window !== 'undefined' ? window.location.origin : ''
      },
      "areaServed": client.serviceAreas.map(area => ({
        "@type": "City",
        "name": `${area.city}, ${area.state}`
      })),
      "offers": {
        "@type": "Offer",
        "description": service.description,
        "priceSpecification": {
          "@type": "PriceSpecification",
          "description": service.basePrice
        }
      },
      "category": service.category,
      "serviceType": service.name
    };
  };

  const generateServiceLocationSchema = () => {
    if (!service || !location) return null;
    
    return {
      "@context": "https://schema.org",
      "@type": "Service",
      "name": `${service.name} in ${location.city}, ${location.state}`,
      "description": `Professional ${service.name.toLowerCase()} services in ${location.city}, ${location.state}. ${service.longDescription}`,
      "provider": {
        "@type": "LocalBusiness",
        "name": client.name,
        "telephone": client.contact.phone,
        "address": {
          "@type": "PostalAddress",
          "addressLocality": location.city,
          "addressRegion": location.state
        }
      },
      "areaServed": {
        "@type": "City",
        "name": `${location.city}, ${location.state}`,
        "geo": {
          "@type": "GeoCoordinates",
          "latitude": location.coordinates.lat,
          "longitude": location.coordinates.lng
        }
      },
      "offers": {
        "@type": "Offer",
        "description": `${service.name} services in ${location.city}`,
        "priceSpecification": {
          "@type": "PriceSpecification",
          "description": service.basePrice
        },
        "availableAtOrFrom": {
          "@type": "Place",
          "name": `${location.city}, ${location.state}`,
          "geo": {
            "@type": "GeoCoordinates",
            "latitude": location.coordinates.lat,
            "longitude": location.coordinates.lng
          }
        }
      },
      "serviceType": service.name,
      "category": service.category
    };
  };

  let schema;
  switch (type) {
    case 'business':
      schema = generateBusinessSchema();
      break;
    case 'service':
      schema = generateServiceSchema();
      break;
    case 'serviceLocation':
      schema = generateServiceLocationSchema();
      break;
    default:
      return null;
  }

  if (!schema) return null;

  return (
    <Helmet>
      <script type="application/ld+json">
        {JSON.stringify(schema)}
      </script>
    </Helmet>
  );
};

export default StructuredData;
