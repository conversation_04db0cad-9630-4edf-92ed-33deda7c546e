import React, { useEffect, useState } from 'react';
import { Browser<PERSON>outer, Routes, Route, Navigate } from 'react-router-dom';
import { <PERSON><PERSON><PERSON><PERSON>rovider } from 'react-helmet-async';
import { ClientConfig, initializeClientConfig } from '../utils/clientConfig';

// Import existing components
import {
  Navigation,
  HeroSection,
  SocialProof,
  Gallery,
  About,
  Reviews,
  BookingSection,
  Footer,
  PrivacyPolicy,
  TermsOfService,
  Visualizer,
  VoiceAI
} from './index';

// Import new template components
import ServicePage from './templates/ServicePage';
import LocationPage from './templates/LocationPage';
import ServiceLocationPage from './templates/ServiceLocationPage';
import SEOHead from './seo/SEOHead';
import LoadingSpinner from './LoadingSpinner';
import AdminApp from '../admin/AdminApp';

interface RouterProps {
  children?: React.ReactNode;
}

const AppRouter: React.FC<RouterProps> = () => {
  const [clientConfig, setClientConfig] = useState<ClientConfig | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const initClient = async () => {
      try {
        const config = await initializeClientConfig();
        setClientConfig(config);
      } catch (err) {
        console.error('Failed to initialize client config:', err);
        setError('Failed to load client configuration');
      } finally {
        setLoading(false);
      }
    };

    initClient();
  }, []);

  if (loading) {
    return <LoadingSpinner />;
  }

  if (error || !clientConfig) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Configuration Error</h1>
          <p className="text-gray-600">{error || 'Client configuration not found'}</p>
        </div>
      </div>
    );
  }

  return (
    <HelmetProvider>
      <BrowserRouter>
        <div className="min-h-screen">
          <Routes>
            {/* Home page - existing template */}
            <Route 
              path="/" 
              element={
                <>
                  <SEOHead 
                    title={clientConfig.seo.title}
                    description={clientConfig.seo.description}
                    keywords={clientConfig.seo.keywords}
                  />
                  <Navigation />
                  <main>
                    <section id="home">
                      <HeroSection />
                    </section>
                    <SocialProof />
                    <Gallery />
                    <About />
                    <Reviews />
                    <BookingSection />
                  </main>
                  <Footer />
                </>
              } 
            />

            {/* Existing special pages */}
            <Route path="/privacy" element={<PrivacyPolicy />} />
            <Route path="/terms" element={<TermsOfService />} />
            <Route path="/visualizer" element={<Visualizer />} />
            <Route path="/voice" element={<VoiceAI />} />

            {/* Admin interface */}
            <Route path="/admin" element={<AdminApp />} />

            {/* Service pages */}
            <Route path="/services/:serviceSlug" element={<ServicePage />} />
            
            {/* Location pages */}
            <Route path="/locations/:locationSlug" element={<LocationPage />} />
            
            {/* Service + Location combination pages */}
            <Route path="/services/:serviceSlug/:locationSlug" element={<ServiceLocationPage />} />
            
            {/* Legacy redirects for SEO */}
            <Route path="/service/:serviceSlug" element={<Navigate to="/services/:serviceSlug" replace />} />
            <Route path="/location/:locationSlug" element={<Navigate to="/locations/:locationSlug" replace />} />

            {/* Catch-all redirect to home */}
            <Route path="*" element={<Navigate to="/" replace />} />
          </Routes>
        </div>
      </BrowserRouter>
    </HelmetProvider>
  );
};

export default AppRouter;
