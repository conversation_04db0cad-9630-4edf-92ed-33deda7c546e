import React, { useEffect, useState } from 'react';
import { useParams, Navigate, Link } from 'react-router-dom';
import { MapPin, Users, Home, DollarSign } from 'lucide-react';

// Import existing components
import {
  Navigation,
  Gallery,
  Reviews,
  BookingSection,
  Footer
} from '../index';

// Import new components
import SEOHead from '../seo/SEOHead';
import StructuredData from '../seo/StructuredData';
import Breadcrumbs from '../seo/Breadcrumbs';
import { getCurrentClient, getLocations, getServices, Location, Service } from '../../utils/clientConfig';

const LocationPage: React.FC = () => {
  const { locationSlug } = useParams<{ locationSlug: string }>();
  const [location, setLocation] = useState<Location | null>(null);
  const [services, setServices] = useState<Service[]>([]);
  const [loading, setLoading] = useState(true);
  const client = getCurrentClient();

  useEffect(() => {
    const loadData = async () => {
      try {
        const [locations, servicesData] = await Promise.all([
          getLocations(),
          getServices()
        ]);
        
        const foundLocation = locations.find(l => l.slug === locationSlug);
        setLocation(foundLocation || null);
        setServices(servicesData);
      } catch (error) {
        console.error('Failed to load location data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (locationSlug) {
      loadData();
    }
  }, [locationSlug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!location || !client) {
    return <Navigate to="/" replace />;
  }

  const seoTitle = `Home Services in ${location.city}, ${location.state} | ${client.name}`;
  const seoDescription = `Professional home improvement services in ${location.city}, ${location.state}. ${client.name} serves ${location.city} with quality construction, renovation, and repair services.`;

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <>
      <SEOHead 
        title={seoTitle}
        description={seoDescription}
        keywords={[`home services ${location.city}`, `contractors ${location.city}`, `${location.city} ${location.state}`]}
      />
      <StructuredData type="business" location={location} />
      
      <div className="min-h-screen">
        <Navigation />
        
        <main>
          {/* Hero Section */}
          <section className="bg-gradient-to-br from-primary-50 to-primary-100 py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <Breadcrumbs 
                items={[
                  { label: 'Locations', href: '/locations' },
                  { label: `${location.city}, ${location.state}`, current: true }
                ]}
              />
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Professional Home Services in {location.city}, {location.state}
                  </h1>
                  <p className="text-xl text-gray-600 mb-8">
                    {location.description}
                  </p>
                  
                  <div className="grid grid-cols-2 gap-4 mb-8">
                    <div className="bg-white rounded-lg p-4 shadow-md">
                      <div className="flex items-center mb-2">
                        <Users className="w-5 h-5 text-blue-600 mr-2" />
                        <span className="font-semibold text-gray-900">Population</span>
                      </div>
                      <p className="text-2xl font-bold text-blue-600">
                        {location.population.toLocaleString()}
                      </p>
                    </div>
                    
                    <div className="bg-white rounded-lg p-4 shadow-md">
                      <div className="flex items-center mb-2">
                        <Home className="w-5 h-5 text-green-600 mr-2" />
                        <span className="font-semibold text-gray-900">Median Home Value</span>
                      </div>
                      <p className="text-2xl font-bold text-green-600">
                        {formatCurrency(location.marketInfo.medianHomeValue)}
                      </p>
                    </div>
                  </div>
                  
                  <a 
                    href="#contact"
                    className="inline-block bg-primary-600 text-white px-8 py-4 rounded-lg font-semibold hover:bg-primary-700 transition-colors duration-300"
                  >
                    Get Free Estimate
                  </a>
                </div>
                
                <div>
                  <img 
                    src={`https://images.unsplash.com/photo-1449824913935-59a10b8d2000?w=600&h=400&fit=crop`}
                    alt={`${location.city}, ${location.state}`}
                    className="rounded-lg shadow-xl w-full h-96 object-cover"
                  />
                </div>
              </div>
            </div>
          </section>

          {/* Services in Location */}
          <section className="py-16 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Our Services in {location.city}
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  We provide comprehensive home improvement services throughout {location.city} and surrounding areas.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {services.map((service) => (
                  <div key={service.id} className="bg-white rounded-lg shadow-lg overflow-hidden hover:shadow-xl transition-shadow duration-300">
                    <img 
                      src={`https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=400&h=250&fit=crop`}
                      alt={service.name}
                      className="w-full h-48 object-cover"
                    />
                    <div className="p-6">
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        {service.name} in {location.city}
                      </h3>
                      <p className="text-gray-600 mb-4">
                        {service.description}
                      </p>
                      <div className="flex items-center justify-between">
                        <span className="text-lg font-semibold text-primary-600">
                          {service.basePrice}
                        </span>
                        <Link 
                          to={`/services/${service.slug}/${location.slug}`}
                          className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 transition-colors duration-300"
                        >
                          Learn More
                        </Link>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Neighborhoods */}
          <section className="py-16 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Neighborhoods We Serve in {location.city}
                </h2>
                <p className="text-xl text-gray-600">
                  We provide services throughout {location.city} including these popular neighborhoods:
                </p>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {location.neighborhoods.map((neighborhood, index) => (
                  <div key={index} className="bg-white rounded-lg p-4 shadow-md text-center">
                    <MapPin className="w-6 h-6 text-primary-600 mx-auto mb-2" />
                    <h3 className="font-semibold text-gray-900">{neighborhood}</h3>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Market Info */}
          <section className="py-16 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Why {location.city} Homeowners Choose Us
                </h2>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="bg-blue-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <Users className="w-8 h-8 text-blue-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Local Expertise</h3>
                  <p className="text-gray-600">
                    We understand {location.city}'s unique building requirements and architectural styles.
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="bg-green-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <Home className="w-8 h-8 text-green-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Quality Workmanship</h3>
                  <p className="text-gray-600">
                    Professional services that enhance your {location.city} home's value and appeal.
                  </p>
                </div>
                
                <div className="text-center">
                  <div className="bg-purple-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <DollarSign className="w-8 h-8 text-purple-600" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-2">Fair Pricing</h3>
                  <p className="text-gray-600">
                    Competitive rates that respect {location.city} homeowners' investment in quality.
                  </p>
                </div>
              </div>
            </div>
          </section>

          {/* Existing components */}
          <Gallery />
          <Reviews />
          <BookingSection />
        </main>
        
        <Footer />
      </div>
    </>
  );
};

export default LocationPage;
