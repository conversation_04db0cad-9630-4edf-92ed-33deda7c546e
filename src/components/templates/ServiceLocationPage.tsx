import React, { useEffect, useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { CheckCircle, MapPin, Clock, Star, Phone } from 'lucide-react';

// Import existing components
import {
  Navigation,
  Gallery,
  Reviews,
  BookingSection,
  Footer
} from '../index';

// Import new components
import SEOHead from '../seo/SEOHead';
import StructuredData from '../seo/StructuredData';
import Breadcrumbs from '../seo/Breadcrumbs';
import { getCurrentClient, getServices, getLocations, Service, Location } from '../../utils/clientConfig';

const ServiceLocationPage: React.FC = () => {
  const { serviceSlug, locationSlug } = useParams<{ serviceSlug: string; locationSlug: string }>();
  const [service, setService] = useState<Service | null>(null);
  const [location, setLocation] = useState<Location | null>(null);
  const [loading, setLoading] = useState(true);
  const client = getCurrentClient();

  useEffect(() => {
    const loadData = async () => {
      try {
        const [services, locations] = await Promise.all([
          getServices(),
          getLocations()
        ]);
        
        const foundService = services.find(s => s.slug === serviceSlug);
        const foundLocation = locations.find(l => l.slug === locationSlug);
        
        setService(foundService || null);
        setLocation(foundLocation || null);
      } catch (error) {
        console.error('Failed to load data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (serviceSlug && locationSlug) {
      loadData();
    }
  }, [serviceSlug, locationSlug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!service || !location || !client) {
    return <Navigate to="/" replace />;
  }

  const seoTitle = `${service.name} in ${location.city}, ${location.state} | ${client.name}`;
  const seoDescription = `Professional ${service.name.toLowerCase()} services in ${location.city}, ${location.state}. ${client.name} provides expert ${service.name.toLowerCase()} with local expertise and guaranteed satisfaction.`;

  return (
    <>
      <SEOHead 
        title={seoTitle}
        description={seoDescription}
        keywords={[...service.keywords, `${service.name} ${location.city}`, `${location.city} ${location.state}`]}
      />
      <StructuredData type="serviceLocation" service={service} location={location} />
      
      <div className="min-h-screen">
        <Navigation />
        
        <main>
          {/* Hero Section */}
          <section className="bg-gradient-to-br from-primary-50 to-primary-100 py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <Breadcrumbs 
                items={[
                  { label: 'Services', href: '/services' },
                  { label: service.name, href: `/services/${service.slug}` },
                  { label: `${location.city}, ${location.state}`, current: true }
                ]}
              />
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Expert {service.name} in {location.city}, {location.state}
                  </h1>
                  <p className="text-xl text-gray-600 mb-8">
                    Get top-quality {service.name.toLowerCase()} services from local experts in {location.city}. 
                    Licensed, insured, and committed to excellence.
                  </p>
                  
                  <div className="flex flex-wrap gap-4 mb-8">
                    <div className="flex items-center text-green-600">
                      <CheckCircle className="w-5 h-5 mr-2" />
                      <span className="font-medium">Licensed & Insured</span>
                    </div>
                    <div className="flex items-center text-blue-600">
                      <Star className="w-5 h-5 mr-2" />
                      <span className="font-medium">5-Star Rated</span>
                    </div>
                    <div className="flex items-center text-purple-600">
                      <MapPin className="w-5 h-5 mr-2" />
                      <span className="font-medium">Local {location.city} Team</span>
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-lg p-6 shadow-lg">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Get Your Free {service.name} Estimate
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Ready to start your {service.name.toLowerCase()} project in {location.city}? 
                      Contact us today for a free, no-obligation estimate.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-3">
                      <a 
                        href={`tel:${client.contact.phone}`}
                        className="flex items-center justify-center bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors duration-300"
                      >
                        <Phone className="w-5 h-5 mr-2" />
                        Call Now
                      </a>
                      <a 
                        href="#contact"
                        className="flex items-center justify-center border-2 border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-300"
                      >
                        Get Free Estimate
                      </a>
                    </div>
                  </div>
                </div>
                
                <div>
                  <img 
                    src={`https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=600&h=400&fit=crop`}
                    alt={`${service.name} in ${location.city}, ${location.state}`}
                    className="rounded-lg shadow-xl w-full h-96 object-cover"
                  />
                </div>
              </div>
            </div>
          </section>

          {/* Service Details */}
          <section className="py-16 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
                <div className="lg:col-span-2">
                  <h2 className="text-3xl font-bold text-gray-900 mb-6">
                    Professional {service.name} Services in {location.city}
                  </h2>
                  <p className="text-lg text-gray-600 mb-8">
                    Need professional {service.name.toLowerCase()} in {location.city}, {location.state}? 
                    {client.name} is your trusted local provider of {service.name.toLowerCase()} services. 
                    We've been serving {location.city} homeowners with quality {service.name.toLowerCase()} solutions.
                  </p>
                  
                  <div className="mb-8">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      Why Choose Our {service.name} Services in {location.city}?
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {service.features.map((feature, index) => (
                        <div key={index} className="flex items-start">
                          <CheckCircle className="w-5 h-5 text-green-600 mr-3 mt-1 flex-shrink-0" />
                          <span className="text-gray-700">{feature}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                  
                  <div className="bg-gray-50 rounded-lg p-6">
                    <h3 className="text-xl font-semibold text-gray-900 mb-4">
                      Local {location.city} Expertise
                    </h3>
                    <p className="text-gray-600">
                      Our {location.city}-based team understands local building requirements and has extensive 
                      experience with {service.name.toLowerCase()} projects throughout {location.city} and {location.state}. 
                      We specialize in {service.name.toLowerCase()} for {location.city} homes, offering {service.features.slice(0, 3).join(', ').toLowerCase()}. 
                      Every project includes our satisfaction guarantee and professional warranty.
                    </p>
                  </div>
                </div>
                
                <div>
                  <div className="bg-primary-50 rounded-lg p-6 mb-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Service Information</h3>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <Clock className="w-5 h-5 text-primary-600 mr-3" />
                        <div>
                          <span className="font-medium text-gray-900">Timeline:</span>
                          <p className="text-gray-600">{service.serviceTime}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <MapPin className="w-5 h-5 text-primary-600 mr-3" />
                        <div>
                          <span className="font-medium text-gray-900">Service Area:</span>
                          <p className="text-gray-600">{location.city}, {location.state}</p>
                        </div>
                      </div>
                      <div className="flex items-center">
                        <Star className="w-5 h-5 text-primary-600 mr-3" />
                        <div>
                          <span className="font-medium text-gray-900">Starting Price:</span>
                          <p className="text-gray-600">{service.basePrice}</p>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="bg-white border-2 border-primary-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">
                      Ready to Get Started?
                    </h3>
                    <p className="text-gray-600 mb-4">
                      Contact us today for your free {service.name.toLowerCase()} estimate in {location.city}.
                    </p>
                    <a 
                      href={`tel:${client.contact.phone}`}
                      className="block text-center bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors duration-300 mb-3"
                    >
                      {client.contact.phone}
                    </a>
                    <a 
                      href="#contact"
                      className="block text-center border-2 border-primary-600 text-primary-600 px-6 py-3 rounded-lg font-semibold hover:bg-primary-50 transition-colors duration-300"
                    >
                      Request Estimate
                    </a>
                  </div>
                </div>
              </div>
            </div>
          </section>

          {/* Neighborhoods Served */}
          <section className="py-16 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  {service.name} Services Throughout {location.city}
                </h2>
                <p className="text-xl text-gray-600">
                  We provide {service.name.toLowerCase()} services in these {location.city} neighborhoods:
                </p>
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                {location.neighborhoods.map((neighborhood, index) => (
                  <div key={index} className="bg-white rounded-lg p-4 shadow-md text-center">
                    <MapPin className="w-6 h-6 text-primary-600 mx-auto mb-2" />
                    <h3 className="font-semibold text-gray-900">{neighborhood}</h3>
                    <p className="text-sm text-gray-600">{service.name} Services</p>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Existing components */}
          <Gallery />
          <Reviews />
          <BookingSection />
        </main>
        
        <Footer />
      </div>
    </>
  );
};

export default ServiceLocationPage;
