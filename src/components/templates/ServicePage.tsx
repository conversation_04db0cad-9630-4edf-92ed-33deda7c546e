import React, { useEffect, useState } from 'react';
import { useParams, Navigate } from 'react-router-dom';
import { CheckCircle, Clock, DollarSign, Star } from 'lucide-react';

// Import existing components
import {
  Navigation,
  Gallery,
  Reviews,
  BookingSection,
  Footer
} from '../index';

// Import new components
import SEOHead from '../seo/SEOHead';
import StructuredData from '../seo/StructuredData';
import Breadcrumbs from '../seo/Breadcrumbs';
import { getCurrentClient, getServices, Service } from '../../utils/clientConfig';

const ServicePage: React.FC = () => {
  const { serviceSlug } = useParams<{ serviceSlug: string }>();
  const [service, setService] = useState<Service | null>(null);
  const [loading, setLoading] = useState(true);
  const client = getCurrentClient();

  useEffect(() => {
    const loadService = async () => {
      try {
        const services = await getServices();
        const foundService = services.find(s => s.slug === serviceSlug);
        setService(foundService || null);
      } catch (error) {
        console.error('Failed to load service:', error);
      } finally {
        setLoading(false);
      }
    };

    if (serviceSlug) {
      loadService();
    }
  }, [serviceSlug]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (!service || !client) {
    return <Navigate to="/" replace />;
  }

  const seoTitle = `Professional ${service.name} Services | ${client.name}`;
  const seoDescription = `Expert ${service.name.toLowerCase()} services in ${client.serviceAreas[0]?.city || 'your area'}. ${service.description} Get your free estimate today!`;

  return (
    <>
      <SEOHead 
        title={seoTitle}
        description={seoDescription}
        keywords={service.keywords}
      />
      <StructuredData type="service" service={service} />
      
      <div className="min-h-screen">
        <Navigation />
        
        <main>
          {/* Hero Section */}
          <section className="bg-gradient-to-br from-primary-50 to-primary-100 py-16">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <Breadcrumbs 
                items={[
                  { label: 'Services', href: '/services' },
                  { label: service.name, current: true }
                ]}
              />
              
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
                <div>
                  <h1 className="text-4xl md:text-5xl font-bold text-gray-900 mb-6">
                    Professional {service.name} Services
                  </h1>
                  <p className="text-xl text-gray-600 mb-8">
                    {service.longDescription}
                  </p>
                  
                  <div className="flex flex-wrap gap-4 mb-8">
                    <div className="flex items-center text-green-600">
                      <CheckCircle className="w-5 h-5 mr-2" />
                      <span className="font-medium">Licensed & Insured</span>
                    </div>
                    <div className="flex items-center text-blue-600">
                      <Star className="w-5 h-5 mr-2" />
                      <span className="font-medium">5-Star Rated</span>
                    </div>
                    <div className="flex items-center text-purple-600">
                      <Clock className="w-5 h-5 mr-2" />
                      <span className="font-medium">{service.serviceTime}</span>
                    </div>
                  </div>
                  
                  <div className="bg-white rounded-lg p-6 shadow-lg">
                    <div className="flex items-center mb-4">
                      <DollarSign className="w-6 h-6 text-green-600 mr-2" />
                      <span className="text-lg font-semibold text-gray-900">
                        {service.basePrice}
                      </span>
                    </div>
                    <p className="text-gray-600 mb-4">
                      Get a free, no-obligation estimate for your {service.name.toLowerCase()} project.
                    </p>
                    <a 
                      href="#contact"
                      className="inline-block bg-primary-600 text-white px-6 py-3 rounded-lg font-semibold hover:bg-primary-700 transition-colors duration-300"
                    >
                      Get Free Estimate
                    </a>
                  </div>
                </div>
                
                <div className="lg:order-first">
                  <img 
                    src={`https://images.unsplash.com/photo-1416879595882-3373a0480b5b?w=600&h=400&fit=crop`}
                    alt={`${service.name} services`}
                    className="rounded-lg shadow-xl w-full h-96 object-cover"
                  />
                </div>
              </div>
            </div>
          </section>

          {/* Features Section */}
          <section className="py-16 bg-white">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  Why Choose Our {service.name} Services?
                </h2>
                <p className="text-xl text-gray-600 max-w-3xl mx-auto">
                  We deliver exceptional {service.name.toLowerCase()} services with attention to detail and customer satisfaction.
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {service.features.map((feature, index) => (
                  <div key={index} className="text-center">
                    <div className="bg-primary-100 rounded-full w-16 h-16 flex items-center justify-center mx-auto mb-4">
                      <CheckCircle className="w-8 h-8 text-primary-600" />
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {feature}
                    </h3>
                    <p className="text-gray-600">
                      Professional {feature.toLowerCase()} for your {service.name.toLowerCase()} project.
                    </p>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Service Areas */}
          <section className="py-16 bg-gray-50">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
              <div className="text-center mb-12">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">
                  {service.name} Service Areas
                </h2>
                <p className="text-xl text-gray-600">
                  We provide {service.name.toLowerCase()} services throughout the following areas:
                </p>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {client.serviceAreas.map((area, index) => (
                  <div key={index} className="bg-white rounded-lg p-6 shadow-md">
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">
                      {area.city}, {area.state}
                    </h3>
                    <p className="text-gray-600 mb-3">
                      Professional {service.name.toLowerCase()} services in {area.city} and surrounding areas.
                    </p>
                    <div className="text-sm text-gray-500">
                      <p>Neighborhoods: {area.neighborhoods.slice(0, 3).join(', ')}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </section>

          {/* Existing components */}
          <Gallery />
          <Reviews />
          <BookingSection />
        </main>
        
        <Footer />
      </div>
    </>
  );
};

export default ServicePage;
