@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ensure Inter font is applied */
@layer base {
  html {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  }
}

/* Prevent FOUC (Flash of Unstyled Content) and ensure proper responsive behavior */
@layer base {
  /* Use higher specificity instead of !important to override external scripts */
  html.plt-desktop,
  html.plt-mobile,
  html.plt-tablet,
  html[mode="md"],
  html[mode="ios"],
  html[mode="android"] {
    /* Reset external framework styles with specificity */
    display: block;
    width: 100%;
    height: auto;
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  html {
    overflow-x: hidden;
    /* Ensure proper viewport behavior on all devices */
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    width: 100%;
    height: auto;
  }

  html:not(.loaded) {
    visibility: hidden;
    opacity: 0;
  }

  html.loaded {
    visibility: visible;
    opacity: 1;
    transition: opacity 0.15s ease-in-out;
  }

  /* Force proper responsive behavior */
  * {
    box-sizing: border-box;
    -webkit-box-sizing: border-box;
    -moz-box-sizing: border-box;
  }

  body {
    overflow-x: hidden;
    scrollbar-gutter: stable;
  }

  * {
    box-sizing: border-box;
  }

  /* Ensure containers don't exceed viewport width */
  .max-w-7xl {
    max-width: min(80rem, 100vw);
  }
}

/* Ensure Tailwind responsive classes work with higher specificity */
@layer utilities {
  /* Use CSS containment to isolate our layout from external interference */
  /* Note: Removed containment as it can interfere with fixed positioning */
  body {
    /* contain: layout style; - Commented out as it interferes with chat widget positioning */
  }

  /* Higher specificity selectors for responsive grid classes */
  html body .grid.md\:grid-cols-2 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  html body .grid.md\:grid-cols-3 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  html body .grid.md\:grid-cols-4 {
    grid-template-columns: repeat(1, minmax(0, 1fr));
  }

  @media (min-width: 768px) {
    html body .grid.md\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    html body .grid.md\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    html body .grid.md\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  @media (min-width: 1024px) {
    html body .grid.lg\:grid-cols-2 {
      grid-template-columns: repeat(2, minmax(0, 1fr));
    }

    html body .grid.lg\:grid-cols-3 {
      grid-template-columns: repeat(3, minmax(0, 1fr));
    }

    html body .grid.lg\:grid-cols-4 {
      grid-template-columns: repeat(4, minmax(0, 1fr));
    }
  }

  /* Higher specificity for flex responsive classes */
  @media (min-width: 768px) {
    html body .md\:flex-row {
      flex-direction: row;
    }

    html body .md\:flex-col {
      flex-direction: column;
    }

    html body .md\:block,
    html body .hidden.md\:block {
      display: block;
    }

    html body .md\:hidden {
      display: none;
    }

    /* Ensure navigation shows on desktop */
    html body nav .hidden.md\:block {
      display: block;
    }
  }

  @media (min-width: 1024px) {
    html body .lg\:flex-row {
      flex-direction: row;
    }

    html body .lg\:flex-col {
      flex-direction: column;
    }

    html body .lg\:block,
    html body .hidden.lg\:block {
      display: block;
    }

    html body .lg\:hidden {
      display: none;
    }

    /* Ensure navigation elements show on large screens */
    html body nav .hidden.lg\:inline {
      display: inline;
    }
  }
}

/* Clean iframe integration for HighLevel forms */
@layer utilities {
  /* Remove any default iframe styling that might interfere */
  iframe[src*="leadconnectorhq.com"] {
    display: block !important;
    border: none !important;
    outline: none !important;
    background: transparent !important;
  }

  /* Ensure booking and form iframes display cleanly */
  iframe[src*="widget/form"],
  iframe[src*="widget/booking"] {
    border: none !important;
    outline: none !important;
    background: white !important;
    /* Ensure proper interaction on all devices */
    pointer-events: auto !important;
    touch-action: auto !important;
    /* Prevent any interference from parent elements */
    isolation: isolate;
  }

  /* Mobile optimization for forms */
  @media (max-width: 640px) {
    iframe[src*="widget/form"] {
      min-height: 500px;
    }

    iframe[src*="widget/booking"] {
      min-height: 600px;
      /* Ensure proper touch interaction on mobile */
      touch-action: auto !important;
      pointer-events: auto !important;
      /* Prevent any overlay issues */
      position: relative !important;
      z-index: 1 !important;
    }
  }

  /* Tablet optimization */
  @media (min-width: 641px) and (max-width: 1024px) {
    iframe[src*="widget/form"] {
      height: 580px !important;
    }

    iframe[src*="widget/booking"] {
      height: 700px !important;
    }
  }

  /* Ensure booking iframe container allows proper interaction */
  div:has(iframe[src*="widget/booking"]),
  div[class*="booking"],
  #VK5p3BfnXq2LNHpzyHj6_booking {
    pointer-events: auto !important;
    touch-action: auto !important;
    position: relative !important;
    z-index: 1 !important;
  }
}

/* Chat Widget Fixed Positioning - Bottom Right of Viewport */
@layer utilities {
  /* Make HighLevel chat widget fixed to viewport - multiple selectors for reliability */
  div[data-widget-id],
  div[id*="chat"],
  div[class*="chat-widget"],
  div[class*="leadconnector-chat"],
  div[class*="lc-chat"],
  iframe[src*="chat-widget"] {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
    /* Ensure it's relative to viewport, not page content */
    transform: none !important;
    margin: 0 !important;
  }

  /* Ensure chat widget button is always visible and clickable */
  button[class*="chat"],
  div[class*="chat-button"],
  div[data-widget-id] button,
  div[class*="chat-launcher"],
  div[class*="chat-toggle"] {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
    transform: none !important;
    margin: 0 !important;
  }

  /* Additional HighLevel specific selectors with viewport positioning */
  .leadconnector-chat-widget,
  .lc-chat-widget,
  #leadconnector-chat {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
    transform: none !important;
    margin: 0 !important;
  }

  /* Override any absolute positioning that might be applied by the widget */
  body div[data-widget-id],
  body div[class*="chat"],
  body iframe[src*="chat"] {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
  }

  /* Ensure chat widget is not affected by container positioning */
  * div[data-widget-id] {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
    /* Remove any transforms that might affect positioning */
    transform: none !important;
    /* Ensure it's not clipped by parent containers */
    clip: auto !important;
    overflow: visible !important;
  }

  /* Force chat widget to viewport positioning regardless of parent containers */
  html body * div[data-widget-id="6855e1482a1b4b63f8da16b6"],
  html body * script[data-widget-id="6855e1482a1b4b63f8da16b6"] + div,
  div[data-widget-id="6855e1482a1b4b63f8da16b6"],
  html body * div[data-widget-id="66b1878b92c831569eb2c07a"],
  html body * script[data-widget-id="66b1878b92c831569eb2c07a"] + div,
  div[data-widget-id="66b1878b92c831569eb2c07a"] {
    position: fixed !important;
    bottom: 20px !important;
    right: 20px !important;
    z-index: 9999 !important;
    transform: none !important;
    margin: 0 !important;
  }
}

/* Custom Form Styling using semantic color system */
@layer components {
  .custom-form-input {
    @apply w-full h-9 px-4 py-2 border-2 border-neutral-300 rounded-md text-sm text-neutral-700 placeholder-neutral-400 focus:border-primary-500 focus:outline-none transition-colors duration-200;
  }

  .custom-form-button {
    @apply w-full h-14 bg-primary-600 hover:bg-primary-700 disabled:bg-neutral-400 disabled:cursor-not-allowed text-white font-bold text-base rounded-md transition-colors duration-200 transform hover:scale-[1.02] active:scale-[0.98];
  }

  .custom-form-upload {
    @apply relative flex flex-col items-center justify-center w-full h-20 border-2 border-dashed border-neutral-300 rounded-md cursor-pointer transition-colors duration-200 hover:border-neutral-400;
  }
}

/* Google Places Autocomplete Styling - Using CSS custom properties to avoid CORS issues */
:root {
  --pac-z-index: 9999;
  --pac-border-radius: 8px;
  --pac-border-color: #d1d5db; /* neutral-300 */
  --pac-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --pac-margin-top: 2px;
  --pac-item-padding: 12px 16px;
  --pac-item-border: 1px solid #f3f4f6; /* neutral-100 */
  --pac-item-font-size: 14px;
  --pac-hover-bg: #f9fafb; /* neutral-50 */
  --pac-selected-bg: #eff6ff; /* primary-50 (blue) */
  --pac-matched-color: #2563eb; /* primary-600 (blue) */
}

/* Apply styles without using !important to avoid CORS conflicts */
.pac-container {
  z-index: var(--pac-z-index);
  border-radius: var(--pac-border-radius);
  border: 1px solid var(--pac-border-color);
  box-shadow: var(--pac-shadow);
  margin-top: var(--pac-margin-top);
  font-family: 'Inter', sans-serif;
}

.pac-item {
  padding: var(--pac-item-padding);
  border-bottom: var(--pac-item-border);
  cursor: pointer;
  font-size: var(--pac-item-font-size);
}

.pac-item:hover {
  background-color: var(--pac-hover-bg);
}

.pac-item-selected {
  background-color: var(--pac-selected-bg);
}

.pac-matched {
  font-weight: 600;
  color: var(--pac-matched-color);
}

/* Hide the Google Places dropdown when not needed */
.pac-container.pac-hidden {
  display: none !important;
}

.custom-form-checkbox {
  @apply w-4 h-4 mt-0.5 text-primary-600 bg-primary-50 border-neutral-300 rounded focus:ring-primary-500 focus:ring-2;
}

/* Smooth loading animation */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

/* Form field focus states */
.form-field:focus-within {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(37, 99, 235, 0.15); /* primary-600 (blue) with opacity */
}

/* Button hover effects */
.btn-primary {
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

/* Hide scrollbar but keep scroll functionality */
::-webkit-scrollbar {
  width: 0px;
  background: transparent;
}

/* For Firefox */
html {
  scrollbar-width: none;
}

/* Ensure proper text rendering */
* {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Loading state for forms */
.form-loading {
  position: relative;
}

.form-loading::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: inherit;
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .custom-form-input {
    border-width: 3px;
  }

  .custom-form-button {
    border: 2px solid transparent;
  }

  .custom-form-button:focus {
    border-color: white;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* FINAL OVERRIDE: Chat Widget Positioning - Must be at the end to override everything */
/* This rule has maximum specificity and comes last to ensure it works */
html body div[data-widget-id="6855e1482a1b4b63f8da16b6"],
html body * div[data-widget-id="6855e1482a1b4b63f8da16b6"],
html body div[data-widget-id="6855e1482a1b4b63f8da16b6"] *,
body div[data-widget-id="6855e1482a1b4b63f8da16b6"],
div[data-widget-id="6855e1482a1b4b63f8da16b6"],
html body div[data-widget-id="66b1878b92c831569eb2c07a"],
html body * div[data-widget-id="66b1878b92c831569eb2c07a"],
html body div[data-widget-id="66b1878b92c831569eb2c07a"] *,
body div[data-widget-id="66b1878b92c831569eb2c07a"],
div[data-widget-id="66b1878b92c831569eb2c07a"] {
  position: fixed !important;
  bottom: 20px !important;
  right: 20px !important;
  z-index: 99999 !important;
  transform: none !important;
  margin: 0 !important;
  padding: 0 !important;
  top: auto !important;
  left: auto !important;
  width: auto !important;
  height: auto !important;
  display: block !important;
}

/* Ensure navbar positioning works */
nav[class*="fixed"] {
  position: fixed !important;
  top: 0 !important;
  z-index: 50 !important;
}

nav[class*="sticky"] {
  position: sticky !important;
  top: 0 !important;
  z-index: 50 !important;
}

/* Force navbar transparency - override any conflicting styles */
nav[style*="transparent"] {
  background-color: transparent !important;
  background: transparent !important;
  background-image: none !important;
}

nav[style*="white"] {
  background-color: white !important;
  background: white !important;
}


