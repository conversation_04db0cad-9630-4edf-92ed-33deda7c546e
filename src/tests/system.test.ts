/**
 * System Integration Tests for Multi-Client Local SEO System
 * 
 * These tests verify that all components work together correctly
 * and that existing integrations are preserved.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { 
  ClientConfig, 
  Service, 
  Location,
  loadClientConfig,
  getServices,
  getLocations,
  getCurrentClient
} from '../utils/clientConfig';
import { generateClientPages, generateSitemap } from '../utils/pageGenerator';
import { generateAIContent } from '../utils/aiContent';

// Mock data for testing
const mockClient: ClientConfig = {
  id: 'test-client',
  name: 'Test Construction Co',
  domain: 'testconstruction.com',
  subdomain: 'test',
  branding: {
    logo: '/logo.svg',
    primaryColor: '#2563eb',
    secondaryColor: '#1e40af',
    accentColor: '#3b82f6',
    favicon: '/favicon.ico'
  },
  contact: {
    phone: '+****************',
    email: '<EMAIL>',
    address: '123 Test St, Test City, TS 12345',
    businessName: 'Test Construction Co LLC',
    licenseNumber: 'TEST123456'
  },
  integrations: {
    highlevel: {
      webhookUrl: 'https://test-webhook.com',
      locationId: 'test-location-id',
      chatWidgetId: 'test-chat-widget',
      formEmbedCode: '<!-- test form -->'
    },
    googlePlaces: {
      apiKey: 'test-google-api-key'
    },
    cloudflare: {
      imageUploadUrl: 'https://test-worker.workers.dev',
      authToken: 'test-auth-token'
    },
    vapi: {
      assistantId: 'test-assistant-id',
      publicKey: 'test-public-key'
    }
  },
  seo: {
    title: 'Professional Construction Services | Test Construction Co',
    description: 'Expert construction services in Test City and surrounding areas.',
    keywords: ['construction', 'renovation', 'building'],
    businessType: 'LocalBusiness',
    serviceType: 'Contractor'
  },
  serviceAreas: [
    {
      city: 'Test City',
      state: 'TS',
      zipCodes: ['12345', '12346'],
      neighborhoods: ['Downtown', 'Uptown']
    }
  ],
  services: [
    {
      id: 'construction',
      name: 'Construction',
      slug: 'construction',
      description: 'Professional construction services',
      price: 'Starting at $10,000',
      features: ['Licensed', 'Insured', 'Quality Work']
    }
  ],
  content: {
    hero: {
      title: 'Professional Construction Services',
      subtitle: 'Quality Work You Can Trust',
      description: 'Expert construction services for your home or business.'
    },
    about: {
      title: 'About Test Construction Co',
      description: 'We provide quality construction services.',
      stats: [
        { number: '100+', label: 'Projects Completed' },
        { number: '10+', label: 'Years Experience' }
      ]
    },
    gallery: [
      'https://example.com/image1.jpg',
      'https://example.com/image2.jpg'
    ]
  }
};

const mockServices: Service[] = [
  {
    id: 'deck-building',
    name: 'Deck Building',
    slug: 'deck-building',
    category: 'construction',
    description: 'Professional deck construction services',
    longDescription: 'Expert deck building with quality materials and craftsmanship.',
    keywords: ['deck building', 'deck construction', 'outdoor decks'],
    basePrice: 'Starting at $5,000',
    features: ['Custom Design', 'Quality Materials', 'Professional Installation'],
    serviceTime: '2-4 weeks',
    popular: true
  },
  {
    id: 'home-renovation',
    name: 'Home Renovation',
    slug: 'home-renovation',
    category: 'renovation',
    description: 'Complete home renovation services',
    longDescription: 'Comprehensive home renovation from planning to completion.',
    keywords: ['home renovation', 'remodeling', 'home improvement'],
    basePrice: 'Custom Quote',
    features: ['Full Service', 'Licensed & Insured', 'Quality Guarantee'],
    serviceTime: '4-12 weeks',
    popular: true
  }
];

const mockLocations: Location[] = [
  {
    id: 'atlanta-ga',
    city: 'Atlanta',
    state: 'GA',
    stateCode: 'GA',
    slug: 'atlanta-ga',
    population: 498715,
    zipCodes: ['30301', '30302', '30303'],
    neighborhoods: ['Midtown', 'Buckhead', 'Virginia-Highland'],
    coordinates: { lat: 33.7490, lng: -84.3880 },
    description: 'Professional services in Atlanta, Georgia',
    marketInfo: {
      medianHomeValue: 350000,
      homeownershipRate: 0.45,
      averageHouseholdIncome: 65000
    }
  },
  {
    id: 'marietta-ga',
    city: 'Marietta',
    state: 'GA',
    stateCode: 'GA',
    slug: 'marietta-ga',
    population: 60972,
    zipCodes: ['30060', '30061', '30062'],
    neighborhoods: ['Downtown Marietta', 'East Cobb', 'West Cobb'],
    coordinates: { lat: 33.9526, lng: -84.5499 },
    description: 'Quality services in historic Marietta',
    marketInfo: {
      medianHomeValue: 425000,
      homeownershipRate: 0.72,
      averageHouseholdIncome: 85000
    }
  }
];

const mockContentTemplates = {
  pageTemplates: {
    servicePage: {
      title: 'Professional {SERVICE} Services | {BUSINESS_NAME}',
      description: 'Expert {SERVICE} services in {PRIMARY_LOCATION}. {BUSINESS_NAME} provides quality {SERVICE} with guaranteed satisfaction.',
      h1: 'Professional {SERVICE} Services'
    },
    locationPage: {
      title: 'Home Services in {CITY}, {STATE} | {BUSINESS_NAME}',
      description: 'Professional home improvement services in {CITY}, {STATE}.',
      h1: 'Home Services in {CITY}, {STATE}'
    },
    serviceLocationPage: {
      title: '{SERVICE} in {CITY}, {STATE} | {BUSINESS_NAME}',
      description: 'Professional {SERVICE} services in {CITY}, {STATE}.',
      h1: '{SERVICE} Services in {CITY}, {STATE}'
    }
  }
};

describe('Multi-Client Local SEO System', () => {
  describe('Client Configuration', () => {
    it('should load and validate client configuration', () => {
      expect(mockClient.id).toBe('test-client');
      expect(mockClient.name).toBe('Test Construction Co');
      expect(mockClient.integrations.highlevel.webhookUrl).toBeTruthy();
      expect(mockClient.integrations.googlePlaces.apiKey).toBeTruthy();
      expect(mockClient.integrations.cloudflare.imageUploadUrl).toBeTruthy();
    });

    it('should preserve all existing integrations', () => {
      const integrations = mockClient.integrations;
      
      // HighLevel integration
      expect(integrations.highlevel.webhookUrl).toContain('webhook');
      expect(integrations.highlevel.locationId).toBeTruthy();
      
      // Google Places integration
      expect(integrations.googlePlaces.apiKey).toBeTruthy();
      
      // Cloudflare integration
      expect(integrations.cloudflare.imageUploadUrl).toContain('workers.dev');
      expect(integrations.cloudflare.authToken).toBeTruthy();
      
      // Vapi integration (optional)
      expect(integrations.vapi?.assistantId).toBeTruthy();
      expect(integrations.vapi?.publicKey).toBeTruthy();
    });
  });

  describe('Page Generation', () => {
    it('should generate correct number of pages', async () => {
      const pages = await generateClientPages(
        mockClient,
        mockServices,
        mockLocations,
        mockContentTemplates
      );

      // Expected pages:
      // 1 home + 2 services + 2 locations + (2 services × 2 locations) = 9 pages
      expect(pages).toHaveLength(9);
      
      // Verify page types
      const homePages = pages.filter(p => p.type === 'home');
      const servicePages = pages.filter(p => p.type === 'service');
      const locationPages = pages.filter(p => p.type === 'location');
      const serviceLocationPages = pages.filter(p => p.type === 'serviceLocation');
      
      expect(homePages).toHaveLength(1);
      expect(servicePages).toHaveLength(2);
      expect(locationPages).toHaveLength(2);
      expect(serviceLocationPages).toHaveLength(4);
    });

    it('should generate unique URLs for all pages', async () => {
      const pages = await generateClientPages(
        mockClient,
        mockServices,
        mockLocations,
        mockContentTemplates
      );

      const urls = pages.map(p => p.path);
      const uniqueUrls = new Set(urls);
      
      expect(uniqueUrls.size).toBe(urls.length);
    });

    it('should generate SEO-optimized content', async () => {
      const pages = await generateClientPages(
        mockClient,
        mockServices,
        mockLocations,
        mockContentTemplates
      );

      pages.forEach(page => {
        expect(page.title).toBeTruthy();
        expect(page.description).toBeTruthy();
        expect(page.keywords).toBeDefined();
        expect(page.keywords.length).toBeGreaterThan(0);
      });
    });
  });

  describe('SEO Features', () => {
    it('should generate valid sitemap', async () => {
      const pages = await generateClientPages(
        mockClient,
        mockServices,
        mockLocations,
        mockContentTemplates
      );

      const sitemap = generateSitemap(pages, 'https://testconstruction.com');
      
      expect(sitemap).toContain('<?xml version="1.0" encoding="UTF-8"?>');
      expect(sitemap).toContain('<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">');
      expect(sitemap).toContain('<loc>https://testconstruction.com/</loc>');
      expect(sitemap).toContain('<priority>1.0</priority>'); // Home page priority
    });

    it('should set correct page priorities', async () => {
      const pages = await generateClientPages(
        mockClient,
        mockServices,
        mockLocations,
        mockContentTemplates
      );

      const sitemap = generateSitemap(pages, 'https://testconstruction.com');
      
      // Home page should have highest priority
      expect(sitemap).toContain('<priority>1.0</priority>');
      
      // Service+location pages should have high priority
      expect(sitemap).toContain('<priority>0.9</priority>');
      
      // Service pages should have medium priority
      expect(sitemap).toContain('<priority>0.8</priority>');
      
      // Location pages should have lower priority
      expect(sitemap).toContain('<priority>0.7</priority>');
    });
  });

  describe('AI Content Integration', () => {
    it('should generate AI content with fallback', async () => {
      const request = {
        type: 'serviceLocation' as const,
        service: mockServices[0],
        location: mockLocations[0],
        client: mockClient
      };

      const response = await generateAIContent(request);
      
      expect(response.success).toBe(true);
      expect(response.content).toBeTruthy();
      expect(response.content!.length).toBeGreaterThan(50);
    });

    it('should handle AI failures gracefully', async () => {
      const request = {
        type: 'service' as const,
        service: mockServices[0],
        client: mockClient
      };

      // Mock AI failure by using invalid configuration
      const response = await generateAIContent(request, { 
        provider: 'invalid' as any 
      });
      
      expect(response.success).toBe(true); // Should succeed with fallback
      expect(response.fallbackUsed).toBe(true);
      expect(response.content).toBeTruthy();
    });
  });

  describe('Template System', () => {
    it('should replace template variables correctly', () => {
      const template = '{SERVICE} in {CITY}, {STATE} | {BUSINESS_NAME}';
      const variables = {
        SERVICE: 'Deck Building',
        CITY: 'Atlanta',
        STATE: 'GA',
        BUSINESS_NAME: 'Test Construction Co'
      };

      let result = template;
      Object.entries(variables).forEach(([key, value]) => {
        const regex = new RegExp(`{${key}}`, 'g');
        result = result.replace(regex, value);
      });

      expect(result).toBe('Deck Building in Atlanta, GA | Test Construction Co');
    });
  });

  describe('URL Structure', () => {
    it('should generate SEO-friendly URLs', async () => {
      const pages = await generateClientPages(
        mockClient,
        mockServices,
        mockLocations,
        mockContentTemplates
      );

      const serviceLocationPage = pages.find(p => p.type === 'serviceLocation');
      expect(serviceLocationPage?.path).toMatch(/^\/services\/[a-z-]+\/[a-z-]+$/);
      
      const servicePage = pages.find(p => p.type === 'service');
      expect(servicePage?.path).toMatch(/^\/services\/[a-z-]+$/);
      
      const locationPage = pages.find(p => p.type === 'location');
      expect(locationPage?.path).toMatch(/^\/locations\/[a-z-]+$/);
    });
  });
});

// Integration test to verify existing components still work
describe('Existing Integration Preservation', () => {
  it('should preserve HighLevel webhook configuration', () => {
    const webhookUrl = mockClient.integrations.highlevel.webhookUrl;
    const locationId = mockClient.integrations.highlevel.locationId;
    
    expect(webhookUrl).toBeTruthy();
    expect(locationId).toBeTruthy();
    
    // Verify URL format
    expect(typeof webhookUrl).toBe('string');
    expect(typeof locationId).toBe('string');
  });

  it('should preserve Google Places API configuration', () => {
    const apiKey = mockClient.integrations.googlePlaces.apiKey;
    
    expect(apiKey).toBeTruthy();
    expect(typeof apiKey).toBe('string');
  });

  it('should preserve Cloudflare Workers configuration', () => {
    const { imageUploadUrl, authToken } = mockClient.integrations.cloudflare;
    
    expect(imageUploadUrl).toBeTruthy();
    expect(authToken).toBeTruthy();
    expect(imageUploadUrl).toContain('workers.dev');
  });

  it('should preserve Vapi AI configuration', () => {
    const vapi = mockClient.integrations.vapi;
    
    expect(vapi?.assistantId).toBeTruthy();
    expect(vapi?.publicKey).toBeTruthy();
  });
});
