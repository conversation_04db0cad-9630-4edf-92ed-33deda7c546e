/**
 * AI Content Integration System
 * 
 * This utility provides hooks for AI-generated content while maintaining
 * fallback content and existing template structure.
 */

import { Service, Location, ClientConfig } from './clientConfig';

export interface AIContentRequest {
  type: 'service' | 'location' | 'serviceLocation' | 'hero' | 'about';
  service?: Service;
  location?: Location;
  client: ClientConfig;
  template?: string;
  maxLength?: number;
  tone?: 'professional' | 'friendly' | 'authoritative' | 'conversational';
  keywords?: string[];
}

export interface AIContentResponse {
  success: boolean;
  content?: string;
  error?: string;
  fallbackUsed?: boolean;
  metadata?: {
    generatedAt: string;
    model?: string;
    tokens?: number;
  };
}

export interface ContentHook {
  content: string;
  isLoading: boolean;
  error: string | null;
  regenerate: () => Promise<void>;
  useFallback: () => void;
}

/**
 * AI Content Provider Configuration
 */
export interface AIProviderConfig {
  provider: 'openai' | 'anthropic' | 'local' | 'mock';
  apiKey?: string;
  model?: string;
  baseUrl?: string;
  maxTokens?: number;
  temperature?: number;
}

/**
 * Default fallback content templates
 */
const FALLBACK_CONTENT = {
  service: {
    description: "Professional {SERVICE} services with quality workmanship and customer satisfaction guaranteed.",
    longDescription: "Get expert {SERVICE} services from licensed professionals. We deliver exceptional results with attention to detail and commitment to excellence.",
    benefits: [
      "Licensed and insured professionals",
      "Quality materials and workmanship",
      "Competitive pricing",
      "Satisfaction guarantee",
      "Local expertise"
    ]
  },
  location: {
    description: "Serving {CITY}, {STATE} with professional home improvement services.",
    intro: "We proudly serve {CITY}, {STATE} with comprehensive home improvement services. Our local team understands the unique needs of {CITY} homeowners.",
    localExpertise: "As a local business, we understand {CITY}'s building codes, permit requirements, and architectural styles."
  },
  serviceLocation: {
    title: "Professional {SERVICE} in {CITY}, {STATE}",
    description: "Expert {SERVICE} services in {CITY}, {STATE}. Licensed, insured, and committed to excellence.",
    intro: "Need professional {SERVICE} in {CITY}, {STATE}? We're your trusted local provider with extensive experience serving {CITY} homeowners."
  }
};

/**
 * Generate AI content prompt
 */
function generatePrompt(request: AIContentRequest): string {
  const { type, service, location, client, tone = 'professional' } = request;
  
  let prompt = `Write ${tone} content for a home services business. `;
  
  switch (type) {
    case 'service':
      prompt += `Create a compelling description for ${service?.name} services. `;
      prompt += `Business: ${client.name}. `;
      prompt += `Include benefits: ${service?.features.join(', ')}. `;
      prompt += `Service areas: ${client.serviceAreas.map(a => `${a.city}, ${a.state}`).join(', ')}. `;
      break;
      
    case 'location':
      prompt += `Create content about serving ${location?.city}, ${location?.state}. `;
      prompt += `Business: ${client.name}. `;
      prompt += `Population: ${location?.population.toLocaleString()}. `;
      prompt += `Neighborhoods: ${location?.neighborhoods.slice(0, 5).join(', ')}. `;
      break;
      
    case 'serviceLocation':
      prompt += `Create content for ${service?.name} services in ${location?.city}, ${location?.state}. `;
      prompt += `Business: ${client.name}. `;
      prompt += `Combine service expertise with local knowledge. `;
      break;
      
    case 'hero':
      prompt += `Create a compelling hero section for ${client.name}. `;
      prompt += `Services: ${client.services.map(s => s.name).join(', ')}. `;
      prompt += `Service areas: ${client.serviceAreas.map(a => `${a.city}, ${a.state}`).join(', ')}. `;
      break;
      
    case 'about':
      prompt += `Create an about section for ${client.name}. `;
      prompt += `Focus on experience, quality, and customer satisfaction. `;
      break;
  }
  
  if (request.keywords?.length) {
    prompt += `Include these keywords naturally: ${request.keywords.join(', ')}. `;
  }
  
  if (request.maxLength) {
    prompt += `Keep it under ${request.maxLength} characters. `;
  }
  
  prompt += `Make it engaging, SEO-friendly, and focused on local expertise.`;
  
  return prompt;
}

/**
 * Mock AI provider for development/testing
 */
async function mockAIProvider(request: AIContentRequest): Promise<AIContentResponse> {
  // Simulate API delay
  await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
  
  // Simulate occasional failures
  if (Math.random() < 0.1) {
    return {
      success: false,
      error: 'Mock AI provider error for testing'
    };
  }
  
  const { type, service, location, client } = request;
  let content = '';
  
  switch (type) {
    case 'service':
      content = `Transform your space with professional ${service?.name.toLowerCase()} services from ${client.name}. Our experienced team delivers exceptional ${service?.name.toLowerCase()} solutions with guaranteed satisfaction. We use premium materials and proven techniques to ensure lasting results that enhance your property's value and appeal.`;
      break;
      
    case 'location':
      content = `${client.name} proudly serves ${location?.city}, ${location?.state} with comprehensive home improvement services. Our local team understands the unique character of ${location?.city} and delivers personalized solutions that respect the community's architectural heritage while meeting modern standards.`;
      break;
      
    case 'serviceLocation':
      content = `Looking for expert ${service?.name.toLowerCase()} in ${location?.city}, ${location?.state}? ${client.name} combines professional expertise with deep local knowledge to deliver outstanding ${service?.name.toLowerCase()} services throughout ${location?.city}. Our ${location?.city}-based team understands local building requirements and has completed numerous successful projects in the area.`;
      break;
      
    case 'hero':
      content = `Professional home improvement services that transform your vision into reality. ${client.name} delivers exceptional craftsmanship and customer service throughout ${client.serviceAreas.map(a => a.city).join(', ')}.`;
      break;
      
    case 'about':
      content = `${client.name} has been serving homeowners with quality craftsmanship and exceptional service. Our experienced team is committed to delivering projects that exceed expectations while maintaining the highest standards of professionalism and integrity.`;
      break;
      
    default:
      content = 'Professional home services with quality workmanship and customer satisfaction guaranteed.';
  }
  
  return {
    success: true,
    content,
    metadata: {
      generatedAt: new Date().toISOString(),
      model: 'mock-ai-v1',
      tokens: content.length
    }
  };
}

/**
 * Generate fallback content
 */
function generateFallbackContent(request: AIContentRequest): string {
  const { type, service, location, client } = request;
  
  let template = '';
  
  switch (type) {
    case 'service':
      template = FALLBACK_CONTENT.service.longDescription;
      break;
    case 'location':
      template = FALLBACK_CONTENT.location.intro;
      break;
    case 'serviceLocation':
      template = FALLBACK_CONTENT.serviceLocation.intro;
      break;
    case 'hero':
      template = `Professional home services with quality workmanship. ${client.name} serves ${client.serviceAreas.map(a => a.city).join(', ')} with exceptional craftsmanship.`;
      break;
    case 'about':
      template = `${client.name} delivers quality home improvement services with professional expertise and customer satisfaction guaranteed.`;
      break;
    default:
      template = 'Professional home services with quality workmanship and customer satisfaction guaranteed.';
  }
  
  // Replace template variables
  let content = template;
  if (service) {
    content = content.replace(/{SERVICE}/g, service.name);
  }
  if (location) {
    content = content.replace(/{CITY}/g, location.city);
    content = content.replace(/{STATE}/g, location.state);
  }
  content = content.replace(/{BUSINESS_NAME}/g, client.name);
  
  return content;
}

/**
 * Main AI content generation function
 */
export async function generateAIContent(
  request: AIContentRequest,
  config?: AIProviderConfig
): Promise<AIContentResponse> {
  try {
    // For now, use mock provider
    // In production, this would route to the configured AI provider
    const provider = config?.provider || 'mock';
    
    switch (provider) {
      case 'mock':
        return await mockAIProvider(request);
        
      case 'openai':
        // TODO: Implement OpenAI integration
        throw new Error('OpenAI provider not implemented yet');
        
      case 'anthropic':
        // TODO: Implement Anthropic integration
        throw new Error('Anthropic provider not implemented yet');
        
      default:
        throw new Error(`Unknown AI provider: ${provider}`);
    }
    
  } catch (error) {
    console.error('AI content generation failed:', error);
    
    // Return fallback content
    const fallbackContent = generateFallbackContent(request);
    
    return {
      success: true,
      content: fallbackContent,
      fallbackUsed: true,
      error: error instanceof Error ? error.message : 'Unknown error'
    };
  }
}

/**
 * React hook for AI content generation
 */
export function useAIContent(request: AIContentRequest, config?: AIProviderConfig): ContentHook {
  // Note: This hook would need React imports in actual usage
  // For now, returning a mock implementation for testing
  return {
    content: generateFallbackContent(request),
    isLoading: false,
    error: null,
    regenerate: async () => {},
    useFallback: () => {}
  };
}
