/**
 * Page Generation Engine for Multi-Client Local SEO System
 * 
 * This utility generates all possible service + location combination pages
 * for each client while preserving existing integrations.
 */

import { ClientConfig, Service, Location } from './clientConfig';

export interface GeneratedPage {
  path: string;
  title: string;
  description: string;
  keywords: string[];
  type: 'home' | 'service' | 'location' | 'serviceLocation';
  service?: Service;
  location?: Location;
  client: ClientConfig;
  content: {
    h1: string;
    hero: {
      title: string;
      subtitle: string;
      description: string;
    };
    intro: string;
    features?: string[];
    localInfo?: string;
  };
}

export interface PageGenerationOptions {
  includePopularOnly?: boolean;
  maxPagesPerClient?: number;
  excludeServices?: string[];
  excludeLocations?: string[];
}

/**
 * Generate all pages for a single client
 */
export const generateClientPages = async (
  client: ClientConfig,
  services: Service[],
  locations: Location[],
  contentTemplates: any,
  options: PageGenerationOptions = {}
): Promise<GeneratedPage[]> => {
  const pages: GeneratedPage[] = [];
  
  // Filter services and locations based on options
  const filteredServices = services.filter(service => {
    if (options.includePopularOnly && !service.popular) return false;
    if (options.excludeServices?.includes(service.id)) return false;
    return true;
  });
  
  const filteredLocations = locations.filter(location => {
    if (options.excludeLocations?.includes(location.id)) return false;
    return true;
  });

  // 1. Home page
  pages.push(generateHomePage(client, contentTemplates));

  // 2. Service pages
  for (const service of filteredServices) {
    pages.push(generateServicePage(client, service, contentTemplates));
  }

  // 3. Location pages
  for (const location of filteredLocations) {
    pages.push(generateLocationPage(client, location, contentTemplates));
  }

  // 4. Service + Location combination pages
  for (const service of filteredServices) {
    for (const location of filteredLocations) {
      pages.push(generateServiceLocationPage(client, service, location, contentTemplates));
    }
  }

  // Apply max pages limit if specified
  if (options.maxPagesPerClient && pages.length > options.maxPagesPerClient) {
    return pages.slice(0, options.maxPagesPerClient);
  }

  return pages;
};

/**
 * Generate home page
 */
const generateHomePage = (client: ClientConfig, templates: any): GeneratedPage => {
  return {
    path: '/',
    title: client.seo.title,
    description: client.seo.description,
    keywords: client.seo.keywords,
    type: 'home',
    client,
    content: {
      h1: client.content.hero.title,
      hero: client.content.hero,
      intro: client.content.about.description,
      features: client.services.map(s => s.name)
    }
  };
};

/**
 * Generate service page
 */
const generateServicePage = (client: ClientConfig, service: Service, templates: any): GeneratedPage => {
  const template = templates.pageTemplates.servicePage;
  
  return {
    path: `/services/${service.slug}`,
    title: replaceTemplateVars(template.title, { 
      SERVICE: service.name, 
      BUSINESS_NAME: client.name 
    }),
    description: replaceTemplateVars(template.description, {
      SERVICE: service.name,
      PRIMARY_LOCATION: client.serviceAreas[0]?.city || 'your area',
      BUSINESS_NAME: client.name
    }),
    keywords: [...service.keywords, `${service.name} services`, `professional ${service.name}`],
    type: 'service',
    service,
    client,
    content: {
      h1: replaceTemplateVars(template.h1, { SERVICE: service.name }),
      hero: {
        title: replaceTemplateVars(template.hero.title, { SERVICE: service.name }),
        subtitle: replaceTemplateVars(template.hero.subtitle, { SERVICE: service.name }),
        description: replaceTemplateVars(template.hero.description, { SERVICE: service.name })
      },
      intro: replaceTemplateVars(template.content.intro, {
        SERVICE: service.name,
        BUSINESS_NAME: client.name
      }),
      features: service.features
    }
  };
};

/**
 * Generate location page
 */
const generateLocationPage = (client: ClientConfig, location: Location, templates: any): GeneratedPage => {
  const template = templates.pageTemplates.locationPage;
  
  return {
    path: `/locations/${location.slug}`,
    title: replaceTemplateVars(template.title, {
      CITY: location.city,
      STATE: location.state,
      BUSINESS_NAME: client.name
    }),
    description: replaceTemplateVars(template.description, {
      CITY: location.city,
      STATE: location.state,
      BUSINESS_NAME: client.name
    }),
    keywords: [`home services ${location.city}`, `contractors ${location.city}`, `${location.city} ${location.state}`],
    type: 'location',
    location,
    client,
    content: {
      h1: replaceTemplateVars(template.h1, { CITY: location.city, STATE: location.state }),
      hero: {
        title: replaceTemplateVars(template.hero.title, { CITY: location.city }),
        subtitle: replaceTemplateVars(template.hero.subtitle, { CITY: location.city, STATE: location.state }),
        description: replaceTemplateVars(template.hero.description, { CITY: location.city })
      },
      intro: replaceTemplateVars(template.content.intro, {
        BUSINESS_NAME: client.name,
        CITY: location.city,
        STATE: location.state
      }),
      localInfo: replaceTemplateVars(template.content.localExpertise, {
        CITY: location.city
      })
    }
  };
};

/**
 * Generate service + location combination page
 */
const generateServiceLocationPage = (
  client: ClientConfig, 
  service: Service, 
  location: Location, 
  templates: any
): GeneratedPage => {
  const template = templates.pageTemplates.serviceLocationPage;
  
  return {
    path: `/services/${service.slug}/${location.slug}`,
    title: replaceTemplateVars(template.title, {
      SERVICE: service.name,
      CITY: location.city,
      STATE: location.state,
      BUSINESS_NAME: client.name
    }),
    description: replaceTemplateVars(template.description, {
      SERVICE: service.name,
      CITY: location.city,
      STATE: location.state,
      BUSINESS_NAME: client.name
    }),
    keywords: [
      ...service.keywords,
      `${service.name} ${location.city}`,
      `${service.name} ${location.city} ${location.state}`,
      `${location.city} ${service.name}`,
      `professional ${service.name} ${location.city}`
    ],
    type: 'serviceLocation',
    service,
    location,
    client,
    content: {
      h1: replaceTemplateVars(template.h1, {
        SERVICE: service.name,
        CITY: location.city,
        STATE: location.state
      }),
      hero: {
        title: replaceTemplateVars(template.hero.title, {
          SERVICE: service.name,
          CITY: location.city
        }),
        subtitle: replaceTemplateVars(template.hero.subtitle, {
          SERVICE: service.name,
          CITY: location.city,
          STATE: location.state
        }),
        description: replaceTemplateVars(template.hero.description, {
          SERVICE: service.name,
          CITY: location.city
        })
      },
      intro: replaceTemplateVars(template.content.intro, {
        SERVICE: service.name,
        CITY: location.city,
        STATE: location.state,
        BUSINESS_NAME: client.name
      }),
      localInfo: replaceTemplateVars(template.content.serviceDetails, {
        SERVICE: service.name,
        SERVICE_FEATURES: service.features.slice(0, 3).join(', ').toLowerCase()
      })
    }
  };
};

/**
 * Replace template variables in strings
 */
const replaceTemplateVars = (template: string, vars: Record<string, string>): string => {
  let result = template;
  
  Object.entries(vars).forEach(([key, value]) => {
    const regex = new RegExp(`{${key}}`, 'g');
    result = result.replace(regex, value);
  });
  
  return result;
};

/**
 * Generate sitemap for all pages
 */
export const generateSitemap = (pages: GeneratedPage[], baseUrl: string): string => {
  const urls = pages.map(page => {
    const url = `${baseUrl}${page.path}`;
    const priority = page.type === 'home' ? '1.0' : 
                    page.type === 'serviceLocation' ? '0.9' :
                    page.type === 'service' ? '0.8' : '0.7';
    
    return `  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${priority}</priority>
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls}
</urlset>`;
};

/**
 * Generate robots.txt
 */
export const generateRobotsTxt = (baseUrl: string): string => {
  return `User-agent: *
Allow: /

Sitemap: ${baseUrl}/sitemap.xml`;
};
