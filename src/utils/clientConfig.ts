/**
 * Client Configuration Management System
 * 
 * This utility manages client-specific configurations for the multi-client SEO system.
 * It preserves all existing integrations while adding multi-client support.
 */

export interface ClientConfig {
  id: string;
  name: string;
  domain: string;
  subdomain: string;
  branding: {
    logo: string;
    primaryColor: string;
    secondaryColor: string;
    accentColor: string;
    favicon: string;
  };
  contact: {
    phone: string;
    email: string;
    address: string;
    businessName: string;
    licenseNumber?: string;
  };
  integrations: {
    highlevel: {
      webhookUrl: string;
      locationId: string;
      chatWidgetId?: string;
      formEmbedCode?: string;
    };
    googlePlaces: {
      apiKey: string;
    };
    cloudflare: {
      imageUploadUrl: string;
      authToken: string;
    };
    vapi?: {
      assistantId: string;
      publicKey: string;
    };
  };
  seo: {
    title: string;
    description: string;
    keywords: string[];
    businessType: string;
    serviceType: string;
  };
  serviceAreas: Array<{
    city: string;
    state: string;
    zipCodes: string[];
    neighborhoods: string[];
  }>;
  services: Array<{
    id: string;
    name: string;
    slug: string;
    description: string;
    price: string;
    features: string[];
  }>;
  content: {
    hero: {
      title: string;
      subtitle: string;
      description: string;
    };
    about: {
      title: string;
      description: string;
      stats: Array<{
        number: string;
        label: string;
      }>;
    };
    gallery: string[];
  };
}

export interface Service {
  id: string;
  name: string;
  slug: string;
  category: string;
  description: string;
  longDescription: string;
  keywords: string[];
  basePrice: string;
  features: string[];
  serviceTime: string;
  popular: boolean;
}

export interface Location {
  id: string;
  city: string;
  state: string;
  stateCode: string;
  slug: string;
  population: number;
  zipCodes: string[];
  neighborhoods: string[];
  coordinates: {
    lat: number;
    lng: number;
  };
  description: string;
  marketInfo: {
    medianHomeValue: number;
    homeownershipRate: number;
    averageHouseholdIncome: number;
  };
}

// Current client configuration (can be set via environment or routing)
let currentClientConfig: ClientConfig | null = null;

/**
 * Get the current client configuration
 */
export const getCurrentClient = (): ClientConfig | null => {
  return currentClientConfig;
};

/**
 * Set the current client configuration
 */
export const setCurrentClient = (config: ClientConfig): void => {
  currentClientConfig = config;
  
  // Update environment variables for existing integrations
  if (typeof window !== 'undefined') {
    // Store in window for runtime access
    (window as any).__CLIENT_CONFIG__ = config;
  }
};

/**
 * Load client configuration by ID
 */
export const loadClientConfig = async (clientId: string): Promise<ClientConfig> => {
  try {
    // In development, import the JSON directly
    if (import.meta.env.DEV) {
      const config = await import(`../data/clients/${clientId}.json`);
      const clientConfig = config.default || config;
      setCurrentClient(clientConfig);
      return clientConfig;
    }

    // In production, fetch from public directory
    const response = await fetch(`/data/clients/${clientId}.json`);
    if (!response.ok) {
      throw new Error(`Client config not found: ${clientId}`);
    }
    const config = await response.json();
    setCurrentClient(config);
    return config;
  } catch (error) {
    console.error('Failed to load client config:', error);
    throw error;
  }
};

/**
 * Determine client from current URL
 */
export const detectClientFromUrl = (): string => {
  if (typeof window === 'undefined') return 'example-client';

  // In development, always use example client
  if (import.meta.env.DEV) {
    return 'example-client';
  }

  const hostname = window.location.hostname;
  const pathname = window.location.pathname;

  // Check for subdomain routing (e.g., horizon.deckora.com)
  const subdomainMatch = hostname.match(/^([^.]+)\.deckora\.com$/);
  if (subdomainMatch) {
    return `${subdomainMatch[1]}-client`;
  }

  // Check for path-based routing (e.g., deckora.com/horizon/)
  const pathMatch = pathname.match(/^\/([^\/]+)\//);
  if (pathMatch) {
    return `${pathMatch[1]}-client`;
  }

  // Default client
  return 'example-client';
};

/**
 * Get environment variables for current client
 */
export const getClientEnvVars = (client?: ClientConfig) => {
  const config = client || getCurrentClient();
  if (!config) return {};
  
  return {
    VITE_HIGHLEVEL_WEBHOOK_URL: config.integrations.highlevel.webhookUrl,
    VITE_HIGHLEVEL_LOCATION_ID: config.integrations.highlevel.locationId,
    VITE_GOOGLE_PLACES_API_KEY: config.integrations.googlePlaces.apiKey,
    VITE_IMAGE_UPLOAD_URL: config.integrations.cloudflare.imageUploadUrl,
    VITE_IMAGE_UPLOAD_TOKEN: config.integrations.cloudflare.authToken,
    VITE_VAPI_ASSISTANT_ID: config.integrations.vapi?.assistantId,
    VITE_VAPI_PUBLIC_KEY: config.integrations.vapi?.publicKey,
  };
};

/**
 * Initialize client configuration on app startup
 */
export const initializeClientConfig = async (): Promise<ClientConfig> => {
  const clientId = detectClientFromUrl();
  return await loadClientConfig(clientId);
};

/**
 * Get all available services
 */
export const getServices = async (): Promise<Service[]> => {
  try {
    if (import.meta.env.DEV) {
      const data = await import('../data/services.json');
      const services = data.default || data;
      return services.homeServices;
    }

    const response = await fetch('/data/services.json');
    const data = await response.json();
    return data.homeServices;
  } catch (error) {
    console.error('Failed to load services:', error);
    return [];
  }
};

/**
 * Get all available locations
 */
export const getLocations = async (): Promise<Location[]> => {
  try {
    if (import.meta.env.DEV) {
      const data = await import('../data/locations.json');
      const locations = data.default || data;
      return locations.serviceAreas;
    }

    const response = await fetch('/data/locations.json');
    const data = await response.json();
    return data.serviceAreas;
  } catch (error) {
    console.error('Failed to load locations:', error);
    return [];
  }
};

/**
 * Get content templates
 */
export const getContentTemplates = async () => {
  try {
    if (import.meta.env.DEV) {
      const data = await import('../data/content-templates.json');
      return data.default || data;
    }

    const response = await fetch('/data/content-templates.json');
    return await response.json();
  } catch (error) {
    console.error('Failed to load content templates:', error);
    return {};
  }
};
