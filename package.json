{"name": "deckora-multi-client-seo", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "generate:pages": "node scripts/generate-pages.js", "generate:sitemap": "node scripts/generate-sitemap.js", "deploy:prepare": "node scripts/deploy-client.js", "lint": "eslint .", "preview": "vite preview", "admin": "vite --mode admin"}, "dependencies": {"lucide-react": "^0.344.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-helmet-async": "^2.0.5", "react-router-dom": "^6.30.1"}, "devDependencies": {"@eslint/js": "^9.9.1", "@types/react": "^18.3.5", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react": "^4.3.1", "autoprefixer": "^10.4.18", "eslint": "^9.9.1", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.11", "globals": "^15.9.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "^5.5.3", "typescript-eslint": "^8.3.0", "vite": "^5.4.2"}}