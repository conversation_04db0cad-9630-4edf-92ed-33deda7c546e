# Google Places API Key
# Get your API key from: https://console.cloud.google.com/
# Enable the Places API and restrict the key to your domain for security
VITE_GOOGLE_PLACES_API_KEY=your-google-maps-api-key

# Image Upload Configuration
# Cloudflare Workers endpoint for image uploads
VITE_IMAGE_UPLOAD_URL=https://image-uploader.your-worker.workers.dev

# Authentication token for image uploads
VITE_IMAGE_UPLOAD_TOKEN=your-secret-key-here

# HighLevel Integration
# Your HighLevel webhook URL for form submissions
VITE_HIGHLEVEL_WEBHOOK_URL=https://services.leadconnectorhq.com/hooks/YOUR_LOCATION_ID/webhook-trigger/YOUR_TRIGGER_ID

# Your HighLevel location ID
VITE_HIGHLEVEL_LOCATION_ID=YOUR_LOCATION_ID

# Backup webhook for monitoring (optional)
VITE_BACKUP_WEBHOOK_URL=https://your-backup-webhook.com
