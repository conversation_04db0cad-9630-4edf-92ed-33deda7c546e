#!/usr/bin/env node

/**
 * Page Generation Script for Multi-Client Local SEO System
 * 
 * This script generates all possible service + location combination pages
 * for each client and creates the necessary files for static deployment.
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Configuration
const CONFIG = {
  clientsDir: path.join(rootDir, 'src/data/clients'),
  servicesFile: path.join(rootDir, 'src/data/services.json'),
  locationsFile: path.join(rootDir, 'src/data/locations.json'),
  templatesFile: path.join(rootDir, 'src/data/content-templates.json'),
  outputDir: path.join(rootDir, 'generated'),
  maxPagesPerClient: 1000,
  includePopularOnly: false
};

/**
 * Load JSON file
 */
async function loadJSON(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Failed to load ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Get all client configuration files
 */
async function getClientFiles() {
  try {
    const files = await fs.readdir(CONFIG.clientsDir);
    return files.filter(file => file.endsWith('.json'));
  } catch (error) {
    console.error('Failed to read clients directory:', error.message);
    return [];
  }
}

/**
 * Replace template variables in strings
 */
function replaceTemplateVars(template, vars) {
  let result = template;
  
  Object.entries(vars).forEach(([key, value]) => {
    const regex = new RegExp(`{${key}}`, 'g');
    result = result.replace(regex, value);
  });
  
  return result;
}

/**
 * Generate pages for a single client
 */
function generateClientPages(client, services, locations, templates) {
  const pages = [];
  
  // Filter services if needed
  const filteredServices = services.filter(service => {
    if (CONFIG.includePopularOnly && !service.popular) return false;
    return true;
  });

  // 1. Home page
  pages.push({
    path: '/',
    title: client.seo.title,
    description: client.seo.description,
    keywords: client.seo.keywords,
    type: 'home',
    client,
    content: {
      h1: client.content.hero.title,
      hero: client.content.hero,
      intro: client.content.about.description
    }
  });

  // 2. Service pages
  for (const service of filteredServices) {
    const template = templates.pageTemplates.servicePage;
    
    pages.push({
      path: `/services/${service.slug}`,
      title: replaceTemplateVars(template.title, { 
        SERVICE: service.name, 
        BUSINESS_NAME: client.name 
      }),
      description: replaceTemplateVars(template.description, {
        SERVICE: service.name,
        PRIMARY_LOCATION: client.serviceAreas[0]?.city || 'your area',
        BUSINESS_NAME: client.name
      }),
      keywords: [...service.keywords, `${service.name} services`],
      type: 'service',
      service,
      client
    });
  }

  // 3. Location pages
  for (const location of locations) {
    const template = templates.pageTemplates.locationPage;
    
    pages.push({
      path: `/locations/${location.slug}`,
      title: replaceTemplateVars(template.title, {
        CITY: location.city,
        STATE: location.state,
        BUSINESS_NAME: client.name
      }),
      description: replaceTemplateVars(template.description, {
        CITY: location.city,
        STATE: location.state,
        BUSINESS_NAME: client.name
      }),
      keywords: [`home services ${location.city}`, `contractors ${location.city}`],
      type: 'location',
      location,
      client
    });
  }

  // 4. Service + Location combination pages
  for (const service of filteredServices) {
    for (const location of locations) {
      const template = templates.pageTemplates.serviceLocationPage;
      
      pages.push({
        path: `/services/${service.slug}/${location.slug}`,
        title: replaceTemplateVars(template.title, {
          SERVICE: service.name,
          CITY: location.city,
          STATE: location.state,
          BUSINESS_NAME: client.name
        }),
        description: replaceTemplateVars(template.description, {
          SERVICE: service.name,
          CITY: location.city,
          STATE: location.state,
          BUSINESS_NAME: client.name
        }),
        keywords: [
          ...service.keywords,
          `${service.name} ${location.city}`,
          `${location.city} ${service.name}`
        ],
        type: 'serviceLocation',
        service,
        location,
        client
      });
    }
  }

  // Apply max pages limit
  if (CONFIG.maxPagesPerClient && pages.length > CONFIG.maxPagesPerClient) {
    return pages.slice(0, CONFIG.maxPagesPerClient);
  }

  return pages;
}

/**
 * Generate sitemap XML
 */
function generateSitemap(pages, baseUrl) {
  const urls = pages.map(page => {
    const url = `${baseUrl}${page.path}`;
    const priority = page.type === 'home' ? '1.0' : 
                    page.type === 'serviceLocation' ? '0.9' :
                    page.type === 'service' ? '0.8' : '0.7';
    
    return `  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${priority}</priority>
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls}
</urlset>`;
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting page generation...');
  
  // Load data files
  console.log('📁 Loading data files...');
  const [services, locations, templates] = await Promise.all([
    loadJSON(CONFIG.servicesFile),
    loadJSON(CONFIG.locationsFile),
    loadJSON(CONFIG.templatesFile)
  ]);

  if (!services || !locations || !templates) {
    console.error('❌ Failed to load required data files');
    process.exit(1);
  }

  // Get client files
  const clientFiles = await getClientFiles();
  if (clientFiles.length === 0) {
    console.error('❌ No client configuration files found');
    process.exit(1);
  }

  console.log(`📋 Found ${clientFiles.length} client(s)`);
  console.log(`🔧 Found ${services.homeServices.length} services`);
  console.log(`📍 Found ${locations.serviceAreas.length} locations`);

  // Create output directory
  await fs.mkdir(CONFIG.outputDir, { recursive: true });

  let totalPages = 0;

  // Process each client
  for (const clientFile of clientFiles) {
    const clientPath = path.join(CONFIG.clientsDir, clientFile);
    const client = await loadJSON(clientPath);
    
    if (!client) {
      console.warn(`⚠️  Skipping invalid client file: ${clientFile}`);
      continue;
    }

    console.log(`\n🏢 Processing client: ${client.name}`);

    // Generate pages for this client
    const pages = generateClientPages(client, services.homeServices, locations.serviceAreas, templates);
    totalPages += pages.length;

    console.log(`   📄 Generated ${pages.length} pages`);

    // Create client output directory
    const clientOutputDir = path.join(CONFIG.outputDir, client.id);
    await fs.mkdir(clientOutputDir, { recursive: true });

    // Save pages data
    await fs.writeFile(
      path.join(clientOutputDir, 'pages.json'),
      JSON.stringify(pages, null, 2)
    );

    // Generate sitemap
    const baseUrl = `https://${client.domain}`;
    const sitemap = generateSitemap(pages, baseUrl);
    await fs.writeFile(
      path.join(clientOutputDir, 'sitemap.xml'),
      sitemap
    );

    // Generate robots.txt
    const robotsTxt = `User-agent: *
Allow: /

Sitemap: ${baseUrl}/sitemap.xml`;
    await fs.writeFile(
      path.join(clientOutputDir, 'robots.txt'),
      robotsTxt
    );

    console.log(`   ✅ Generated sitemap and robots.txt`);
  }

  console.log(`\n🎉 Page generation complete!`);
  console.log(`📊 Total pages generated: ${totalPages}`);
  console.log(`📁 Output directory: ${CONFIG.outputDir}`);
}

// Run the script
main().catch(error => {
  console.error('❌ Page generation failed:', error);
  process.exit(1);
});
