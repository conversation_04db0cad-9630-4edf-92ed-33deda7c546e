#!/usr/bin/env node

/**
 * Sitemap Generation Script for Multi-Client Local SEO System
 * 
 * This script generates XML sitemaps for each client based on their
 * generated pages and service/location combinations.
 */

import fs from 'fs/promises';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Configuration
const CONFIG = {
  generatedDir: path.join(rootDir, 'generated'),
  clientsDir: path.join(rootDir, 'src/data/clients'),
  outputDir: path.join(rootDir, 'public')
};

/**
 * Load JSON file
 */
async function loadJSON(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Failed to load ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Get all client directories
 */
async function getClientDirectories() {
  try {
    const entries = await fs.readdir(CONFIG.generatedDir, { withFileTypes: true });
    return entries.filter(entry => entry.isDirectory()).map(entry => entry.name);
  } catch (error) {
    console.error('Failed to read generated directory:', error.message);
    return [];
  }
}

/**
 * Generate XML sitemap from pages
 */
function generateSitemapXML(pages, baseUrl) {
  const urls = pages.map(page => {
    const url = `${baseUrl}${page.path}`;
    const lastmod = new Date().toISOString().split('T')[0];
    
    // Set priority based on page type
    let priority = '0.5';
    switch (page.type) {
      case 'home':
        priority = '1.0';
        break;
      case 'serviceLocation':
        priority = '0.9';
        break;
      case 'service':
        priority = '0.8';
        break;
      case 'location':
        priority = '0.7';
        break;
    }
    
    // Set change frequency
    let changefreq = 'weekly';
    if (page.type === 'home') {
      changefreq = 'daily';
    } else if (page.type === 'serviceLocation') {
      changefreq = 'weekly';
    } else {
      changefreq = 'monthly';
    }
    
    return `  <url>
    <loc>${url}</loc>
    <lastmod>${lastmod}</lastmod>
    <changefreq>${changefreq}</changefreq>
    <priority>${priority}</priority>
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"
        xmlns:xhtml="http://www.w3.org/1999/xhtml"
        xmlns:mobile="http://www.google.com/schemas/sitemap-mobile/1.0"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"
        xmlns:video="http://www.google.com/schemas/sitemap-video/1.1">
${urls}
</urlset>`;
}

/**
 * Generate robots.txt content
 */
function generateRobotsTxt(baseUrl) {
  return `User-agent: *
Allow: /

# Disallow admin and internal pages
Disallow: /admin/
Disallow: /_*
Disallow: /api/

# Allow all other pages
Allow: /services/
Allow: /locations/

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Crawl delay (optional)
Crawl-delay: 1`;
}

/**
 * Generate sitemap index for multiple clients (if needed)
 */
function generateSitemapIndex(clients) {
  const sitemaps = clients.map(client => {
    const url = `https://${client.domain}/sitemap.xml`;
    const lastmod = new Date().toISOString().split('T')[0];
    
    return `  <sitemap>
    <loc>${url}</loc>
    <lastmod>${lastmod}</lastmod>
  </sitemap>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemaps}
</sitemapindex>`;
}

/**
 * Process a single client
 */
async function processClient(clientId) {
  console.log(`\n🏢 Processing client: ${clientId}`);
  
  try {
    // Load client configuration
    const clientConfigPath = path.join(CONFIG.clientsDir, `${clientId}.json`);
    const client = await loadJSON(clientConfigPath);
    
    if (!client) {
      console.warn(`   ⚠️  Client configuration not found: ${clientId}`);
      return null;
    }
    
    // Load generated pages
    const pagesPath = path.join(CONFIG.generatedDir, clientId, 'pages.json');
    const pages = await loadJSON(pagesPath);
    
    if (!pages) {
      console.warn(`   ⚠️  Generated pages not found for: ${clientId}`);
      return null;
    }
    
    console.log(`   📄 Found ${pages.length} pages`);
    
    // Generate sitemap
    const baseUrl = `https://${client.domain}`;
    const sitemapXML = generateSitemapXML(pages, baseUrl);
    
    // Save sitemap to generated directory
    const sitemapPath = path.join(CONFIG.generatedDir, clientId, 'sitemap.xml');
    await fs.writeFile(sitemapPath, sitemapXML);
    
    // Generate and save robots.txt
    const robotsTxt = generateRobotsTxt(baseUrl);
    const robotsPath = path.join(CONFIG.generatedDir, clientId, 'robots.txt');
    await fs.writeFile(robotsPath, robotsTxt);
    
    console.log(`   ✅ Generated sitemap and robots.txt`);
    console.log(`   🔗 Sitemap URL: ${baseUrl}/sitemap.xml`);
    
    return {
      id: clientId,
      name: client.name,
      domain: client.domain,
      pageCount: pages.length,
      sitemapUrl: `${baseUrl}/sitemap.xml`
    };
    
  } catch (error) {
    console.error(`   ❌ Failed to process ${clientId}:`, error.message);
    return null;
  }
}

/**
 * Generate summary report
 */
async function generateSummaryReport(results) {
  const validResults = results.filter(r => r !== null);
  
  const report = {
    generatedAt: new Date().toISOString(),
    totalClients: validResults.length,
    totalPages: validResults.reduce((sum, r) => sum + r.pageCount, 0),
    clients: validResults.map(r => ({
      id: r.id,
      name: r.name,
      domain: r.domain,
      pageCount: r.pageCount,
      sitemapUrl: r.sitemapUrl
    }))
  };
  
  const reportPath = path.join(CONFIG.generatedDir, 'sitemap-report.json');
  await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
  
  console.log(`\n📊 Summary Report:`);
  console.log(`   Total clients: ${report.totalClients}`);
  console.log(`   Total pages: ${report.totalPages}`);
  console.log(`   Report saved: ${reportPath}`);
  
  return report;
}

/**
 * Main execution function
 */
async function main() {
  console.log('🗺️  Starting sitemap generation...');
  
  // Get all client directories
  const clientIds = await getClientDirectories();
  
  if (clientIds.length === 0) {
    console.error('❌ No client directories found in generated folder');
    console.log('💡 Run "npm run generate:pages" first to generate pages');
    process.exit(1);
  }
  
  console.log(`📋 Found ${clientIds.length} client(s)`);
  
  // Process each client
  const results = [];
  
  for (const clientId of clientIds) {
    const result = await processClient(clientId);
    results.push(result);
  }
  
  // Generate summary report
  await generateSummaryReport(results);
  
  // Generate master sitemap index if multiple clients
  const validResults = results.filter(r => r !== null);
  if (validResults.length > 1) {
    const sitemapIndex = generateSitemapIndex(validResults);
    const indexPath = path.join(CONFIG.generatedDir, 'sitemap-index.xml');
    await fs.writeFile(indexPath, sitemapIndex);
    console.log(`🗂️  Generated sitemap index: ${indexPath}`);
  }
  
  console.log(`\n🎉 Sitemap generation complete!`);
}

// Run the script
main().catch(error => {
  console.error('❌ Sitemap generation failed:', error);
  process.exit(1);
});
