#!/usr/bin/env node

/**
 * Client Deployment Script for Local SEO System
 * 
 * This script prepares a client-specific deployment by:
 * 1. Generating all pages for the client
 * 2. Creating sitemap and robots.txt
 * 3. Building the application with client-specific environment
 * 4. Preparing deployment artifacts
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Configuration
const CONFIG = {
  clientConfigFile: path.join(rootDir, 'src/data/clients/example-client.json'),
  servicesFile: path.join(rootDir, 'src/data/services.json'),
  locationsFile: path.join(rootDir, 'src/data/locations.json'),
  templatesFile: path.join(rootDir, 'src/data/content-templates.json'),
  buildDir: path.join(rootDir, 'dist'),
  deployDir: path.join(rootDir, 'deploy')
};

/**
 * Load JSON file
 */
async function loadJSON(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Failed to load ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Create environment file for client
 */
async function createClientEnv(client) {
  const envContent = `# Environment variables for ${client.name}
# Generated automatically for deployment

# Google Places API Key
VITE_GOOGLE_PLACES_API_KEY=${client.integrations.googlePlaces.apiKey}

# Image Upload Configuration (Cloudflare Workers)
VITE_IMAGE_UPLOAD_URL=${client.integrations.cloudflare.imageUploadUrl}
VITE_IMAGE_UPLOAD_TOKEN=${client.integrations.cloudflare.authToken}

# HighLevel Integration
VITE_HIGHLEVEL_WEBHOOK_URL=${client.integrations.highlevel.webhookUrl}
VITE_HIGHLEVEL_LOCATION_ID=${client.integrations.highlevel.locationId}

# Vapi Voice AI Integration (if configured)
${client.integrations.vapi ? `VITE_VAPI_ASSISTANT_ID=${client.integrations.vapi.assistantId}` : '# VITE_VAPI_ASSISTANT_ID='}
${client.integrations.vapi ? `VITE_VAPI_PUBLIC_KEY=${client.integrations.vapi.publicKey}` : '# VITE_VAPI_PUBLIC_KEY='}

# Client Configuration
VITE_CLIENT_ID=${client.id}
VITE_CLIENT_NAME=${client.name}
VITE_CLIENT_DOMAIN=${client.domain}
`;

  const envPath = path.join(rootDir, '.env');
  await fs.writeFile(envPath, envContent);
  console.log(`✅ Created environment configuration for ${client.name}`);
}

/**
 * Generate sitemap for client
 */
function generateSitemap(pages, baseUrl) {
  const urls = pages.map(page => {
    const url = `${baseUrl}${page.path}`;
    const priority = page.type === 'home' ? '1.0' : 
                    page.type === 'serviceLocation' ? '0.9' :
                    page.type === 'service' ? '0.8' : '0.7';
    
    return `  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${priority}</priority>
  </url>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urls}
</urlset>`;
}

/**
 * Generate robots.txt
 */
function generateRobotsTxt(baseUrl) {
  return `User-agent: *
Allow: /

# Disallow admin pages
Disallow: /admin/

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml`;
}

/**
 * Generate pages for client
 */
function generatePages(client, services, locations, templates) {
  const pages = [];
  
  // Home page
  pages.push({
    path: '/',
    title: client.seo.title,
    description: client.seo.description,
    type: 'home'
  });

  // Service pages
  for (const service of services.homeServices) {
    if (!service.popular) continue; // Only include popular services for now
    
    pages.push({
      path: `/services/${service.slug}`,
      title: `${service.name} Services | ${client.name}`,
      description: `Professional ${service.name.toLowerCase()} services. ${service.description}`,
      type: 'service'
    });
  }

  // Location pages
  for (const location of locations.serviceAreas) {
    pages.push({
      path: `/locations/${location.slug}`,
      title: `Home Services in ${location.city}, ${location.state} | ${client.name}`,
      description: `Professional home services in ${location.city}, ${location.state}. ${client.name} serves ${location.city} with quality construction and renovation.`,
      type: 'location'
    });
  }

  // Service + Location combinations (popular services only)
  const popularServices = services.homeServices.filter(s => s.popular);
  for (const service of popularServices) {
    for (const location of locations.serviceAreas) {
      pages.push({
        path: `/services/${service.slug}/${location.slug}`,
        title: `${service.name} in ${location.city}, ${location.state} | ${client.name}`,
        description: `Professional ${service.name.toLowerCase()} services in ${location.city}, ${location.state}. Expert ${service.name.toLowerCase()} with local expertise.`,
        type: 'serviceLocation'
      });
    }
  }

  return pages;
}

/**
 * Create deployment package
 */
async function createDeploymentPackage(client, pages) {
  // Create deployment directory
  await fs.mkdir(CONFIG.deployDir, { recursive: true });
  
  // Copy build files
  console.log('📦 Copying build files...');
  await copyDirectory(CONFIG.buildDir, CONFIG.deployDir);
  
  // Generate and save sitemap
  const baseUrl = `https://${client.domain}`;
  const sitemap = generateSitemap(pages, baseUrl);
  await fs.writeFile(path.join(CONFIG.deployDir, 'sitemap.xml'), sitemap);
  
  // Generate and save robots.txt
  const robotsTxt = generateRobotsTxt(baseUrl);
  await fs.writeFile(path.join(CONFIG.deployDir, 'robots.txt'), robotsTxt);
  
  // Create deployment info
  const deployInfo = {
    client: {
      id: client.id,
      name: client.name,
      domain: client.domain
    },
    deployment: {
      date: new Date().toISOString(),
      pages: pages.length,
      version: '1.0.0'
    },
    integrations: {
      highlevel: !!client.integrations.highlevel.webhookUrl,
      googlePlaces: !!client.integrations.googlePlaces.apiKey,
      cloudflare: !!client.integrations.cloudflare.imageUploadUrl,
      vapi: !!client.integrations.vapi?.assistantId
    }
  };
  
  await fs.writeFile(
    path.join(CONFIG.deployDir, 'deployment-info.json'),
    JSON.stringify(deployInfo, null, 2)
  );
  
  console.log(`✅ Created deployment package for ${client.name}`);
  console.log(`📊 Generated ${pages.length} pages`);
  console.log(`📁 Deployment ready in: ${CONFIG.deployDir}`);
}

/**
 * Copy directory recursively
 */
async function copyDirectory(src, dest) {
  await fs.mkdir(dest, { recursive: true });
  
  const entries = await fs.readdir(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      await copyDirectory(srcPath, destPath);
    } else {
      await fs.copyFile(srcPath, destPath);
    }
  }
}

/**
 * Clean up temporary files
 */
async function cleanup() {
  try {
    const envPath = path.join(rootDir, '.env');
    await fs.unlink(envPath);
  } catch (error) {
    // Ignore if file doesn't exist
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting client deployment preparation...');
  
  try {
    // Load configuration files
    console.log('📁 Loading configuration files...');
    const [client, services, locations, templates] = await Promise.all([
      loadJSON(CONFIG.clientConfigFile),
      loadJSON(CONFIG.servicesFile),
      loadJSON(CONFIG.locationsFile),
      loadJSON(CONFIG.templatesFile)
    ]);

    if (!client || !services || !locations || !templates) {
      throw new Error('Failed to load required configuration files');
    }

    console.log(`🏢 Preparing deployment for: ${client.name}`);
    console.log(`🌐 Domain: ${client.domain}`);

    // Generate pages
    console.log('📄 Generating pages...');
    const pages = generatePages(client, services, locations, templates);
    console.log(`   Generated ${pages.length} pages`);

    // Create client environment
    await createClientEnv(client);

    // Build application
    console.log('🔨 Building application...');
    execSync('npm run build', { 
      cwd: rootDir, 
      stdio: 'inherit' 
    });

    // Create deployment package
    await createDeploymentPackage(client, pages);

    console.log('\n🎉 Deployment preparation complete!');
    console.log('\n📋 Next steps:');
    console.log('1. Review the deployment package in ./deploy/');
    console.log('2. Upload to your hosting provider (Netlify, Vercel, etc.)');
    console.log('3. Configure domain and SSL');
    console.log('4. Test all integrations (HighLevel, Google Places, etc.)');

  } catch (error) {
    console.error('❌ Deployment preparation failed:', error.message);
    process.exit(1);
  } finally {
    await cleanup();
  }
}

// Run the script
main().catch(error => {
  console.error('❌ Deployment script failed:', error);
  cleanup();
  process.exit(1);
});
