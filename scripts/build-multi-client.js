#!/usr/bin/env node

/**
 * Multi-Client Build Script for Local SEO System
 * 
 * This script builds separate deployments for each client while preserving
 * all existing integrations (HighLevel, Google Places, Cloudflare, Vapi).
 */

import fs from 'fs/promises';
import path from 'path';
import { execSync } from 'child_process';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const rootDir = path.join(__dirname, '..');

// Configuration
const CONFIG = {
  clientsDir: path.join(rootDir, 'src/data/clients'),
  buildDir: path.join(rootDir, 'dist'),
  multiClientBuildDir: path.join(rootDir, 'dist-multi-client'),
  envTemplate: path.join(rootDir, '.env.example'),
  generatedDir: path.join(rootDir, 'generated')
};

/**
 * Load JSON file
 */
async function loadJSON(filePath) {
  try {
    const content = await fs.readFile(filePath, 'utf-8');
    return JSON.parse(content);
  } catch (error) {
    console.error(`Failed to load ${filePath}:`, error.message);
    return null;
  }
}

/**
 * Get all client configuration files
 */
async function getClientFiles() {
  try {
    const files = await fs.readdir(CONFIG.clientsDir);
    return files.filter(file => file.endsWith('.json'));
  } catch (error) {
    console.error('Failed to read clients directory:', error.message);
    return [];
  }
}

/**
 * Create environment file for client
 */
async function createClientEnv(client) {
  const envContent = `# Environment variables for ${client.name}
# Generated automatically - do not edit manually

# Google Places API Key
VITE_GOOGLE_PLACES_API_KEY=${client.integrations.googlePlaces.apiKey}

# Image Upload Configuration (Cloudflare Workers)
VITE_IMAGE_UPLOAD_URL=${client.integrations.cloudflare.imageUploadUrl}
VITE_IMAGE_UPLOAD_TOKEN=${client.integrations.cloudflare.authToken}

# HighLevel Integration
VITE_HIGHLEVEL_WEBHOOK_URL=${client.integrations.highlevel.webhookUrl}
VITE_HIGHLEVEL_LOCATION_ID=${client.integrations.highlevel.locationId}

# Vapi Voice AI Integration
${client.integrations.vapi ? `VITE_VAPI_ASSISTANT_ID=${client.integrations.vapi.assistantId}` : '# VITE_VAPI_ASSISTANT_ID='}
${client.integrations.vapi ? `VITE_VAPI_PUBLIC_KEY=${client.integrations.vapi.publicKey}` : '# VITE_VAPI_PUBLIC_KEY='}

# Client Configuration
VITE_CLIENT_ID=${client.id}
VITE_CLIENT_NAME=${client.name}
VITE_CLIENT_DOMAIN=${client.domain}
`;

  const envPath = path.join(rootDir, '.env');
  await fs.writeFile(envPath, envContent);
  console.log(`   📝 Created .env for ${client.name}`);
}

/**
 * Copy generated files for client
 */
async function copyGeneratedFiles(client, clientBuildDir) {
  const clientGeneratedDir = path.join(CONFIG.generatedDir, client.id);
  
  try {
    // Copy sitemap.xml
    const sitemapSource = path.join(clientGeneratedDir, 'sitemap.xml');
    const sitemapDest = path.join(clientBuildDir, 'sitemap.xml');
    await fs.copyFile(sitemapSource, sitemapDest);
    
    // Copy robots.txt
    const robotsSource = path.join(clientGeneratedDir, 'robots.txt');
    const robotsDest = path.join(clientBuildDir, 'robots.txt');
    await fs.copyFile(robotsSource, robotsDest);
    
    console.log(`   📋 Copied SEO files for ${client.name}`);
  } catch (error) {
    console.warn(`   ⚠️  Could not copy generated files for ${client.name}:`, error.message);
  }
}

/**
 * Create Netlify configuration for client
 */
async function createNetlifyConfig(client, clientBuildDir) {
  const netlifyConfig = `# Netlify configuration for ${client.name}
# Generated automatically

[build]
  publish = "."
  command = "echo 'Build completed'"

[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"

[[headers]]
  for = "/assets/*"
  [headers.values]
    Cache-Control = "public, max-age=31536000, immutable"

# Environment variables for this client
[build.environment]
  VITE_CLIENT_ID = "${client.id}"
  VITE_CLIENT_NAME = "${client.name}"
  VITE_CLIENT_DOMAIN = "${client.domain}"
`;

  const configPath = path.join(clientBuildDir, 'netlify.toml');
  await fs.writeFile(configPath, netlifyConfig);
  console.log(`   🌐 Created Netlify config for ${client.name}`);
}

/**
 * Build application for a specific client
 */
async function buildClient(client) {
  console.log(`\n🏢 Building for client: ${client.name}`);
  
  try {
    // 1. Create client-specific environment
    await createClientEnv(client);
    
    // 2. Run Vite build
    console.log(`   🔨 Running Vite build...`);
    execSync('npm run build', { 
      cwd: rootDir, 
      stdio: 'pipe' 
    });
    
    // 3. Create client build directory
    const clientBuildDir = path.join(CONFIG.multiClientBuildDir, client.id);
    await fs.mkdir(clientBuildDir, { recursive: true });
    
    // 4. Copy build files
    console.log(`   📦 Copying build files...`);
    await copyDirectory(CONFIG.buildDir, clientBuildDir);
    
    // 5. Copy generated SEO files
    await copyGeneratedFiles(client, clientBuildDir);
    
    // 6. Create deployment configuration
    await createNetlifyConfig(client, clientBuildDir);
    
    // 7. Create client info file
    const clientInfo = {
      id: client.id,
      name: client.name,
      domain: client.domain,
      buildDate: new Date().toISOString(),
      version: '1.0.0'
    };
    await fs.writeFile(
      path.join(clientBuildDir, 'client-info.json'),
      JSON.stringify(clientInfo, null, 2)
    );
    
    console.log(`   ✅ Build completed for ${client.name}`);
    
  } catch (error) {
    console.error(`   ❌ Build failed for ${client.name}:`, error.message);
    throw error;
  }
}

/**
 * Copy directory recursively
 */
async function copyDirectory(src, dest) {
  await fs.mkdir(dest, { recursive: true });
  
  const entries = await fs.readdir(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      await copyDirectory(srcPath, destPath);
    } else {
      await fs.copyFile(srcPath, destPath);
    }
  }
}

/**
 * Clean up build artifacts
 */
async function cleanup() {
  try {
    // Remove temporary .env file
    const envPath = path.join(rootDir, '.env');
    await fs.unlink(envPath);
  } catch (error) {
    // Ignore if file doesn't exist
  }
}

/**
 * Main execution function
 */
async function main() {
  console.log('🚀 Starting multi-client build process...');
  
  // Get client files
  const clientFiles = await getClientFiles();
  if (clientFiles.length === 0) {
    console.error('❌ No client configuration files found');
    process.exit(1);
  }
  
  console.log(`📋 Found ${clientFiles.length} client(s) to build`);
  
  // Clean previous builds
  console.log('🧹 Cleaning previous builds...');
  try {
    await fs.rm(CONFIG.multiClientBuildDir, { recursive: true, force: true });
  } catch (error) {
    // Ignore if directory doesn't exist
  }
  
  await fs.mkdir(CONFIG.multiClientBuildDir, { recursive: true });
  
  // Build each client
  let successCount = 0;
  let failureCount = 0;
  
  for (const clientFile of clientFiles) {
    try {
      const clientPath = path.join(CONFIG.clientsDir, clientFile);
      const client = await loadJSON(clientPath);
      
      if (!client) {
        console.warn(`⚠️  Skipping invalid client file: ${clientFile}`);
        continue;
      }
      
      await buildClient(client);
      successCount++;
      
    } catch (error) {
      console.error(`❌ Failed to build client from ${clientFile}:`, error.message);
      failureCount++;
    }
  }
  
  // Cleanup
  await cleanup();
  
  // Summary
  console.log(`\n🎉 Multi-client build complete!`);
  console.log(`✅ Successful builds: ${successCount}`);
  console.log(`❌ Failed builds: ${failureCount}`);
  console.log(`📁 Output directory: ${CONFIG.multiClientBuildDir}`);
  
  if (failureCount > 0) {
    process.exit(1);
  }
}

// Run the script
main().catch(error => {
  console.error('❌ Multi-client build failed:', error);
  cleanup();
  process.exit(1);
});
