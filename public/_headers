# Cache control for static assets
/assets/*
  Cache-Control: public, max-age=31536000, immutable

# Cache control for HTML files
/*.html
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# Cache control for CSS and JS files
/*.css
  Cache-Control: public, max-age=31536000, immutable
  
/*.js
  Cache-Control: public, max-age=31536000, immutable

# Default cache control
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
