<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Upload Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
        }
        .upload-area {
            border: 2px dashed #ccc;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: border-color 0.3s;
        }
        .upload-area:hover {
            border-color: #007cba;
        }
        .upload-area.dragover {
            border-color: #007cba;
            background-color: #f0f8ff;
        }
        #fileInput {
            display: none;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            display: none;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .loading {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        button {
            background-color: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #005a87;
        }
        button:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .uploaded-image {
            max-width: 100%;
            margin-top: 10px;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <h1>🖼️ Image Upload Test</h1>
    <p>Test your Cloudflare Worker image upload functionality</p>
    
    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <p>📁 Click here or drag and drop an image to upload</p>
        <p style="font-size: 14px; color: #666;">Supported formats: JPEG, PNG, GIF, WebP (Max 10MB)</p>
    </div>
    
    <input type="file" id="fileInput" accept="image/*">
    <button id="uploadBtn" onclick="uploadImage()" disabled>Upload Image</button>
    
    <div id="result" class="result"></div>

    <script>
        const uploadArea = document.querySelector('.upload-area');
        const fileInput = document.getElementById('fileInput');
        const uploadBtn = document.getElementById('uploadBtn');
        const result = document.getElementById('result');
        
        let selectedFile = null;

        // Drag and drop functionality
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                handleFileSelect(files[0]);
            }
        });

        fileInput.addEventListener('change', (e) => {
            if (e.target.files.length > 0) {
                handleFileSelect(e.target.files[0]);
            }
        });

        function handleFileSelect(file) {
            if (!file.type.startsWith('image/')) {
                showResult('Please select an image file.', 'error');
                return;
            }
            
            selectedFile = file;
            uploadBtn.disabled = false;
            uploadArea.innerHTML = `
                <p>✅ Selected: ${file.name}</p>
                <p style="font-size: 14px; color: #666;">Size: ${(file.size / 1024 / 1024).toFixed(2)} MB</p>
            `;
        }

        async function uploadImage() {
            if (!selectedFile) {
                showResult('Please select a file first.', 'error');
                return;
            }

            uploadBtn.disabled = true;
            showResult('Uploading image...', 'loading');

            const formData = new FormData();
            formData.append('image', selectedFile);

            try {
                const response = await fetch('https://image-uploader.mauricio-e1e.workers.dev', {
                    method: 'POST',
                    headers: {
                        'Authorization': 'Bearer test-secret-key'
                    },
                    body: formData
                });

                const data = await response.json();

                if (response.ok && data.success) {
                    showResult(`
                        <strong>✅ Upload successful!</strong><br>
                        <strong>URL:</strong> <a href="${data.url}" target="_blank">${data.url}</a><br>
                        <strong>Filename:</strong> ${data.filename}<br>
                        <img src="${data.url}" alt="Uploaded image" class="uploaded-image">
                    `, 'success');
                } else {
                    showResult(`❌ Upload failed: ${data.error || 'Unknown error'}`, 'error');
                }
            } catch (error) {
                showResult(`❌ Upload failed: ${error.message}`, 'error');
            } finally {
                uploadBtn.disabled = false;
            }
        }

        function showResult(message, type) {
            result.innerHTML = message;
            result.className = `result ${type}`;
            result.style.display = 'block';
        }
    </script>
</body>
</html>
